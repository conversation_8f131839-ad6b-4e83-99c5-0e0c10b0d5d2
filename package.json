{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "docker:build": "docker build -t fansxcluziv .", "docker:run": "docker run -p 3000:3000 fansxcluziv", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "deploy:staging": "./scripts/deploy.sh staging", "deploy:production": "./scripts/deploy.sh production", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist coverage", "prepare": "husky install"}, "dependencies": {"@auth/core": "latest", "@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^3.9.1", "@next-auth/prisma-adapter": "latest", "@opentelemetry/api": "latest", "@opentelemetry/exporter-trace-otlp-http": "latest", "@opentelemetry/instrumentation": "latest", "@opentelemetry/instrumentation-express": "latest", "@opentelemetry/instrumentation-http": "latest", "@opentelemetry/resources": "latest", "@opentelemetry/sdk-trace-base": "latest", "@opentelemetry/sdk-trace-node": "latest", "@opentelemetry/semantic-conventions": "latest", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "cashfree-pg": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "crypto": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "express": "latest", "express-rate-limit": "latest", "http": "latest", "input-otp": "latest", "ioredis": "latest", "lucide-react": "^0.454.0", "next": "14.2.16", "next-auth": "latest", "next-themes": "latest", "nodemailer": "latest", "pino": "latest", "pino-pretty": "latest", "prisma": "latest", "react": "^18", "sharp": "latest", "canvas": "latest", "fluent-ffmpeg": "latest", "node-media-server": "latest", "hls.js": "latest", "isomorphic-dompurify": "latest", "winston": "latest", "react-day-picker": "latest", "react-dom": "^18", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "latest", "socket.io": "latest", "socket.io-client": "latest", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/jest": "^29.5.0", "@types/express": "^4.17.0", "@types/nodemailer": "^6.4.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@playwright/test": "^1.40.0", "babel-jest": "^29.5.0", "cross-env": "^7.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-testing-library": "^6.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^15.0.0", "node-mocks-http": "^1.13.0", "postcss": "^8.5", "prettier": "^3.0.0", "tailwindcss": "^3.4.17", "tsx": "^4.0.0", "typescript": "^5"}}