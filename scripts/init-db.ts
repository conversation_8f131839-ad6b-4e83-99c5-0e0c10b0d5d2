import { prisma } from "../lib/prisma"

async function initializeDatabase() {
  try {
    // Create default categories
    const categories = [
      { name: "Gaming" },
      { name: "Music" },
      { name: "Art" },
      { name: "Technology" },
      { name: "Sports" },
    ]

    for (const category of categories) {
      await prisma.category.upsert({
        where: { name: category.name },
        update: {},
        create: category,
      })
    }

    // Create default tags
    const tags = [
      { name: "Live" },
      { name: "Interactive" },
      { name: "Educational" },
      { name: "Entertainment" },
      { name: "Tutorial" },
    ]

    for (const tag of tags) {
      await prisma.tag.upsert({
        where: { name: tag.name },
        update: {},
        create: tag,
      })
    }

    console.log("Database initialized successfully!")
  } catch (error) {
    console.error("Error initializing database:", error)
  } finally {
    await prisma.$disconnect()
  }
}

initializeDatabase()
