#!/bin/bash

# FansXcluziv Production Deployment Script
# Usage: ./scripts/deploy.sh [environment]

set -e

ENVIRONMENT=${1:-production}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🚀 Starting deployment for environment: $ENVIRONMENT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    command -v docker >/dev/null 2>&1 || { log_error "Docker is required but not installed. Aborting."; exit 1; }
    command -v docker-compose >/dev/null 2>&1 || { log_error "Docker Compose is required but not installed. Aborting."; exit 1; }
    command -v git >/dev/null 2>&1 || { log_error "Git is required but not installed. Aborting."; exit 1; }
    
    log_info "All dependencies are installed."
}

# Backup database
backup_database() {
    log_info "Creating database backup..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups"
    mkdir -p "$BACKUP_DIR"
    
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose exec -T postgres pg_dump -U postgres fansxcluziv > "$BACKUP_FILE"
        log_info "Database backup created: $BACKUP_FILE"
    else
        log_warn "Skipping database backup for non-production environment"
    fi
}

# Pull latest code
update_code() {
    log_info "Updating code from repository..."
    
    cd "$PROJECT_ROOT"
    
    # Stash any local changes
    git stash
    
    # Pull latest changes
    if [ "$ENVIRONMENT" = "production" ]; then
        git checkout main
        git pull origin main
    else
        git checkout develop
        git pull origin develop
    fi
    
    log_info "Code updated successfully."
}

# Build and deploy
deploy_application() {
    log_info "Building and deploying application..."
    
    cd "$PROJECT_ROOT"
    
    # Set environment-specific compose file
    if [ "$ENVIRONMENT" = "production" ]; then
        COMPOSE_FILE="docker-compose.prod.yml"
    elif [ "$ENVIRONMENT" = "staging" ]; then
        COMPOSE_FILE="docker-compose.staging.yml"
    else
        COMPOSE_FILE="docker-compose.yml"
    fi
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_warn "Compose file $COMPOSE_FILE not found, using default docker-compose.yml"
        COMPOSE_FILE="docker-compose.yml"
    fi
    
    # Build images
    log_info "Building Docker images..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    # Stop existing containers
    log_info "Stopping existing containers..."
    docker-compose -f "$COMPOSE_FILE" down
    
    # Start new containers
    log_info "Starting new containers..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Run database migrations
    log_info "Running database migrations..."
    docker-compose -f "$COMPOSE_FILE" exec -T app npx prisma migrate deploy
    
    log_info "Application deployed successfully."
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    MAX_ATTEMPTS=30
    ATTEMPT=1
    
    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        if curl -f http://localhost:3000/api/health >/dev/null 2>&1; then
            log_info "Health check passed!"
            return 0
        fi
        
        log_warn "Health check attempt $ATTEMPT/$MAX_ATTEMPTS failed, retrying in 10 seconds..."
        sleep 10
        ATTEMPT=$((ATTEMPT + 1))
    done
    
    log_error "Health check failed after $MAX_ATTEMPTS attempts"
    return 1
}

# Cleanup old images and containers
cleanup() {
    log_info "Cleaning up old Docker images and containers..."
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused volumes (be careful with this in production)
    if [ "$ENVIRONMENT" != "production" ]; then
        docker volume prune -f
    fi
    
    log_info "Cleanup completed."
}

# Rollback function
rollback() {
    log_error "Deployment failed. Initiating rollback..."
    
    # Get the previous image tag
    PREVIOUS_TAG=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep fansxcluziv | sed -n '2p' | cut -d':' -f2)
    
    if [ -n "$PREVIOUS_TAG" ]; then
        log_info "Rolling back to previous version: $PREVIOUS_TAG"
        
        # Update docker-compose to use previous tag
        # This is a simplified rollback - in production, you'd want more sophisticated versioning
        docker-compose down
        docker-compose up -d
        
        log_info "Rollback completed."
    else
        log_error "No previous version found for rollback."
    fi
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$DISCORD_WEBHOOK_URL" ]; then
        curl -H "Content-Type: application/json" \
             -X POST \
             -d "{\"content\": \"🚀 **FansXcluziv Deployment** \\n**Environment:** $ENVIRONMENT\\n**Status:** $status\\n**Message:** $message\"}" \
             "$DISCORD_WEBHOOK_URL" >/dev/null 2>&1
    fi
    
    # Add other notification methods here (Slack, email, etc.)
}

# Main deployment process
main() {
    log_info "Starting FansXcluziv deployment process..."
    
    # Trap errors and perform rollback
    trap 'rollback; send_notification "❌ FAILED" "Deployment failed and rollback initiated"; exit 1' ERR
    
    check_dependencies
    
    if [ "$ENVIRONMENT" = "production" ]; then
        backup_database
    fi
    
    update_code
    deploy_application
    
    if health_check; then
        cleanup
        send_notification "✅ SUCCESS" "Deployment completed successfully"
        log_info "🎉 Deployment completed successfully!"
    else
        log_error "Deployment failed health check"
        exit 1
    fi
}

# Show usage if no arguments provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [environment]"
    echo "Environments: development, staging, production"
    echo "Example: $0 production"
    exit 1
fi

# Run main function
main "$@"
