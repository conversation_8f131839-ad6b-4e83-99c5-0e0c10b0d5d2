#!/usr/bin/env node

/**
 * API Structure Test for FansXcluziv
 * Tests if all API routes are properly structured
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Testing API Route Structure...\n')

// Define expected API routes and their methods
const expectedRoutes = {
  'app/api/auth/[...nextauth]/route.ts': ['GET', 'POST'],
  'app/api/donations/create/route.ts': ['POST'],
  'app/api/subscriptions/create/route.ts': ['POST'],
  'app/api/payments/webhook/route.ts': ['POST'],
  'app/api/analytics/creator/route.ts': ['GET'],
  'app/api/streaming/status/route.ts': ['GET', 'POST'],
  'app/api/streaming/key/route.ts': ['GET', 'POST'],
  'app/api/users/profile/route.ts': ['GET', 'PUT'],
  'app/api/users/follow/route.ts': ['POST', 'DELETE', 'GET'],
  'app/api/upload/media/route.ts': ['POST', 'GET', 'DELETE'],
  'app/api/notifications/route.ts': ['GET', 'PUT', 'DELETE'],
  'app/api/search/route.ts': ['GET', 'POST'],
  'app/api/health/route.ts': ['GET', 'HEAD'],
  'app/api/metrics/route.ts': ['GET'],
  'app/api/categories/route.ts': ['GET', 'POST'],
  'app/api/tags/route.ts': ['GET', 'POST'],
  'app/api/chat/messages/route.ts': ['GET', 'POST'],
  'app/api/payouts/request/route.ts': ['POST', 'GET'],
  'app/api/streams/live/route.ts': ['GET', 'POST'],
}

let totalRoutes = 0
let validRoutes = 0
let issues = []

console.log('📋 Checking API route files and exports...\n')

for (const [routePath, expectedMethods] of Object.entries(expectedRoutes)) {
  totalRoutes++
  
  if (!fs.existsSync(routePath)) {
    issues.push(`❌ Missing file: ${routePath}`)
    continue
  }
  
  try {
    const content = fs.readFileSync(routePath, 'utf8')
    
    // Check if file has proper exports
    const hasExports = expectedMethods.some(method => 
      content.includes(`export async function ${method}`)
    )
    
    if (!hasExports) {
      issues.push(`⚠️  ${routePath}: No exported HTTP methods found`)
      continue
    }
    
    // Check for specific method exports
    const missingMethods = expectedMethods.filter(method => 
      !content.includes(`export async function ${method}`)
    )
    
    if (missingMethods.length > 0) {
      issues.push(`⚠️  ${routePath}: Missing methods: ${missingMethods.join(', ')}`)
    }
    
    // Check for proper imports
    const hasNextResponse = content.includes('NextResponse')
    const hasPrisma = content.includes('prisma')
    
    if (!hasNextResponse) {
      issues.push(`⚠️  ${routePath}: Missing NextResponse import`)
    }
    
    if (!hasPrisma && !routePath.includes('health') && !routePath.includes('metrics')) {
      issues.push(`⚠️  ${routePath}: Missing Prisma import (may be intentional)`)
    }
    
    // Check for error handling
    const hasErrorHandling = content.includes('try') && content.includes('catch')
    if (!hasErrorHandling) {
      issues.push(`⚠️  ${routePath}: Missing error handling`)
    }
    
    validRoutes++
    console.log(`✅ ${routePath}`)
    
  } catch (error) {
    issues.push(`❌ Error reading ${routePath}: ${error.message}`)
  }
}

console.log('\n📊 API Route Structure Summary:')
console.log(`✅ Valid routes: ${validRoutes}/${totalRoutes}`)

if (issues.length > 0) {
  console.log('\n⚠️  Issues found:')
  issues.forEach(issue => console.log(issue))
} else {
  console.log('\n🎉 All API routes are properly structured!')
}

// Test middleware structure
console.log('\n🛡️  Checking middleware structure...')
const middlewareFiles = [
  'lib/middleware/auth.ts',
  'lib/middleware/rateLimit.ts',
  'lib/middleware/validation.ts',
  'lib/middleware/security.ts',
]

let middlewareIssues = []

middlewareFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    middlewareIssues.push(`❌ Missing middleware: ${file}`)
    return
  }
  
  try {
    const content = fs.readFileSync(file, 'utf8')
    
    // Check for proper exports
    if (!content.includes('export')) {
      middlewareIssues.push(`⚠️  ${file}: No exports found`)
    }
    
    console.log(`✅ ${file}`)
  } catch (error) {
    middlewareIssues.push(`❌ Error reading ${file}: ${error.message}`)
  }
})

if (middlewareIssues.length > 0) {
  console.log('\n⚠️  Middleware issues:')
  middlewareIssues.forEach(issue => console.log(issue))
} else {
  console.log('✅ All middleware files are present')
}

// Test security configurations
console.log('\n🔒 Checking security configurations...')
const securityFiles = [
  'lib/security/watermark.ts',
  'lib/security/moderation.ts',
]

let securityIssues = []

securityFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    securityIssues.push(`❌ Missing security file: ${file}`)
    return
  }
  
  try {
    const content = fs.readFileSync(file, 'utf8')
    
    if (!content.includes('export')) {
      securityIssues.push(`⚠️  ${file}: No exports found`)
    }
    
    console.log(`✅ ${file}`)
  } catch (error) {
    securityIssues.push(`❌ Error reading ${file}: ${error.message}`)
  }
})

if (securityIssues.length > 0) {
  console.log('\n⚠️  Security issues:')
  securityIssues.forEach(issue => console.log(issue))
} else {
  console.log('✅ All security files are present')
}

// Final summary
const totalIssues = issues.length + middlewareIssues.length + securityIssues.length

console.log('\n📋 Final API Structure Test Summary:')
console.log(`✅ API Routes: ${validRoutes}/${totalRoutes} valid`)
console.log(`⚠️  Total Issues: ${totalIssues}`)

if (totalIssues === 0) {
  console.log('\n🎉 All API structure tests passed!')
  console.log('✅ FansXcluziv API is properly structured for production')
} else {
  console.log('\n⚠️  Some API structure issues found. Review above for details.')
}

console.log('\n🚀 Next recommended tests:')
console.log('1. Unit tests for individual API routes')
console.log('2. Integration tests with database')
console.log('3. End-to-end API testing')
console.log('4. Load testing for performance')
