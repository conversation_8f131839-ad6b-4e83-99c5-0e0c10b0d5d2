#!/usr/bin/env node

/**
 * Basic functionality test script for FansXcluziv
 * Tests core components without full build
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Running Basic Functionality Tests...\n')

// Test 1: Check if all required files exist
console.log('📁 Checking required files...')
const requiredFiles = [
  'package.json',
  'next.config.js',
  'tailwind.config.ts',
  'prisma/schema.prisma',
  'lib/prisma.ts',
  'lib/auth.ts',
  'lib/cashfree.ts',
  'app/layout.tsx',
  'app/page.tsx',
  'components/ui/button.tsx',
  'Dockerfile',
  'docker-compose.yml',
]

let missingFiles = []
requiredFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    missingFiles.push(file)
  }
})

if (missingFiles.length === 0) {
  console.log('✅ All required files present')
} else {
  console.log('❌ Missing files:', missingFiles)
}

// Test 2: Check package.json structure
console.log('\n📦 Checking package.json...')
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  
  const requiredScripts = ['dev', 'build', 'start', 'lint', 'test']
  const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script])
  
  if (missingScripts.length === 0) {
    console.log('✅ All required scripts present')
  } else {
    console.log('❌ Missing scripts:', missingScripts)
  }
  
  const requiredDeps = ['next', 'react', 'prisma', '@prisma/client', 'next-auth']
  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
  )
  
  if (missingDeps.length === 0) {
    console.log('✅ All core dependencies present')
  } else {
    console.log('❌ Missing dependencies:', missingDeps)
  }
} catch (error) {
  console.log('❌ Error reading package.json:', error.message)
}

// Test 3: Check environment variables template
console.log('\n🔧 Checking environment configuration...')
if (fs.existsSync('.env.example')) {
  const envExample = fs.readFileSync('.env.example', 'utf8')
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'CASHFREE_APP_ID',
    'CASHFREE_SECRET_KEY'
  ]
  
  const missingEnvVars = requiredEnvVars.filter(envVar => !envExample.includes(envVar))
  
  if (missingEnvVars.length === 0) {
    console.log('✅ All required environment variables documented')
  } else {
    console.log('❌ Missing environment variables:', missingEnvVars)
  }
} else {
  console.log('❌ .env.example file missing')
}

// Test 4: Check API routes structure
console.log('\n🛣️  Checking API routes...')
const apiRoutes = [
  'app/api/auth/[...nextauth]/route.ts',
  'app/api/donations/create/route.ts',
  'app/api/subscriptions/create/route.ts',
  'app/api/payments/webhook/route.ts',
  'app/api/analytics/creator/route.ts',
  'app/api/health/route.ts',
]

const missingRoutes = apiRoutes.filter(route => !fs.existsSync(route))

if (missingRoutes.length === 0) {
  console.log('✅ All core API routes present')
} else {
  console.log('❌ Missing API routes:', missingRoutes)
}

// Test 5: Check Docker configuration
console.log('\n🐳 Checking Docker configuration...')
const dockerFiles = ['Dockerfile', 'docker-compose.yml', '.dockerignore']
const missingDockerFiles = dockerFiles.filter(file => !fs.existsSync(file))

if (missingDockerFiles.length === 0) {
  console.log('✅ Docker configuration complete')
} else {
  console.log('❌ Missing Docker files:', missingDockerFiles)
}

// Test 6: Check database schema
console.log('\n🗄️  Checking database schema...')
try {
  const schema = fs.readFileSync('prisma/schema.prisma', 'utf8')
  const requiredModels = ['User', 'Stream', 'Donation', 'Subscription', 'Follow']
  const missingModels = requiredModels.filter(model => !schema.includes(`model ${model}`))
  
  if (missingModels.length === 0) {
    console.log('✅ All core database models present')
  } else {
    console.log('❌ Missing database models:', missingModels)
  }
} catch (error) {
  console.log('❌ Error reading Prisma schema:', error.message)
}

// Test 7: Check security configurations
console.log('\n🔒 Checking security configurations...')
const securityFiles = [
  'lib/middleware/auth.ts',
  'lib/middleware/rateLimit.ts',
  'lib/middleware/validation.ts',
  'lib/security/watermark.ts',
  'lib/security/moderation.ts',
]

const missingSecurityFiles = securityFiles.filter(file => !fs.existsSync(file))

if (missingSecurityFiles.length === 0) {
  console.log('✅ Security configurations present')
} else {
  console.log('❌ Missing security files:', missingSecurityFiles)
}

// Test 8: Check monitoring setup
console.log('\n📊 Checking monitoring setup...')
const monitoringFiles = [
  'config/prometheus.yml',
  'config/nginx.conf',
  'config/nginx-proxy.conf',
]

const missingMonitoringFiles = monitoringFiles.filter(file => !fs.existsSync(file))

if (missingMonitoringFiles.length === 0) {
  console.log('✅ Monitoring configuration complete')
} else {
  console.log('❌ Missing monitoring files:', missingMonitoringFiles)
}

// Summary
console.log('\n📋 Test Summary:')
const totalTests = 8
const passedTests = [
  missingFiles.length === 0,
  missingRoutes.length === 0,
  missingDockerFiles.length === 0,
  missingSecurityFiles.length === 0,
  missingMonitoringFiles.length === 0,
].filter(Boolean).length

console.log(`✅ Passed: ${passedTests}/${totalTests} tests`)

if (passedTests === totalTests) {
  console.log('🎉 All basic functionality tests passed!')
  console.log('✅ FansXcluziv is ready for production deployment')
} else {
  console.log('⚠️  Some tests failed. Please review the issues above.')
}

console.log('\n🚀 Next steps:')
console.log('1. Run: npm run build (to test full build)')
console.log('2. Run: npm run test (to run unit tests)')
console.log('3. Run: docker-compose up -d (to test containerization)')
console.log('4. Check: http://localhost:3000/api/health (to verify health endpoint)')
