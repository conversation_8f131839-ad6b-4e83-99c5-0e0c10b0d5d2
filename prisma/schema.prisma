generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                String         @id @default(cuid())
  name              String?
  email             String?        @unique
  emailVerified     DateTime?
  image             String?
  hashedPassword    String?
  role              String         @default("user")
  bio               String?
  website           String?
  location          String?
  followersCount    Int            @default(0)
  followingCount    Int            @default(0)
  totalEarnings     Float          @default(0)
  availableBalance  Float          @default(0)
  accounts          Account[]
  sessions          Session[]
  streams           Stream[]
  followers         Follow[]       @relation("UserFollowers")
  following         Follow[]       @relation("UserFollowing")
  chatMessages      ChatMessage[]
  subscriptions     Subscription[] @relation("UserSubscriptions")
  subscribedTo      Subscription[] @relation("CreatorSubscriptions")
  donations         Donation[]     @relation("UserDonations")
  receivedDonations Donation[]     @relation("CreatorDonations")
  payouts           Payout[]
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}

model Follow {
  id          String   @id @default(cuid())
  followerId  String
  followingId String
  follower    User     @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)
  following   User     @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())

  @@unique([followerId, followingId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Stream {
  id           String        @id @default(cuid())
  title        String
  description  String?
  creatorId    String
  creator      User          @relation(fields: [creatorId], references: [id])
  startTime    DateTime      @default(now())
  endTime      DateTime?
  isLive       Boolean       @default(false)
  viewerCount  Int           @default(0)
  totalViews   Int           @default(0)
  streamKey    String        @unique @default(cuid())
  rtmpUrl      String?
  hlsUrl       String?
  tags         Tag[]
  category     Category?     @relation(fields: [categoryId], references: [id])
  categoryId   String?
  chatMessages ChatMessage[]
  donations    Donation[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
}

model ChatMessage {
  id        String   @id @default(cuid())
  content   String
  userId    String
  streamId  String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  stream    Stream   @relation(fields: [streamId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
}

model Tag {
  id      String   @id @default(cuid())
  name    String   @unique
  streams Stream[]
}

model Category {
  id      String   @id @default(cuid())
  name    String   @unique
  streams Stream[]
}

model Subscription {
  id               String            @id @default(cuid())
  userId           String
  creatorId        String
  tier             SubscriptionTier
  amount           Float
  status           SubscriptionStatus @default(ACTIVE)
  currentPeriodEnd DateTime
  user             User              @relation("UserSubscriptions", fields: [userId], references: [id], onDelete: Cascade)
  creator          User              @relation("CreatorSubscriptions", fields: [creatorId], references: [id], onDelete: Cascade)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt

  @@unique([userId, creatorId])
}

model Donation {
  id        String        @id @default(cuid())
  amount    Float
  message   String?
  userId    String
  creatorId String
  streamId  String?
  status    PaymentStatus @default(PENDING)
  user      User          @relation("UserDonations", fields: [userId], references: [id], onDelete: Cascade)
  creator   User          @relation("CreatorDonations", fields: [creatorId], references: [id], onDelete: Cascade)
  stream    Stream?       @relation(fields: [streamId], references: [id], onDelete: SetNull)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
}

model Payout {
  id          String       @id @default(cuid())
  userId      String
  amount      Float
  status      PayoutStatus @default(PENDING)
  bankAccount String?
  ifscCode    String?
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

enum SubscriptionTier {
  BASIC
  PREMIUM
  VIP
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  REFUNDED
}

enum PayoutStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}
