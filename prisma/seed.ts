import { PrismaClient } from "@prisma/client"
import { hash } from "bcryptjs"

const prisma = new PrismaClient()

async function main() {
  console.log("🌱 Starting database seeding...")

  // Create categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name: "Gaming" },
      update: {},
      create: { name: "Gaming" },
    }),
    prisma.category.upsert({
      where: { name: "Music" },
      update: {},
      create: { name: "Music" },
    }),
    prisma.category.upsert({
      where: { name: "Art" },
      update: {},
      create: { name: "Art" },
    }),
    prisma.category.upsert({
      where: { name: "Fitness" },
      update: {},
      create: { name: "Fitness" },
    }),
    prisma.category.upsert({
      where: { name: "Lifestyle" },
      update: {},
      create: { name: "Lifestyle" },
    }),
    prisma.category.upsert({
      where: { name: "Education" },
      update: {},
      create: { name: "Education" },
    }),
  ])

  console.log("✅ Categories created")

  // Create tags
  const tags = await Promise.all([
    prisma.tag.upsert({
      where: { name: "live" },
      update: {},
      create: { name: "live" },
    }),
    prisma.tag.upsert({
      where: { name: "interactive" },
      update: {},
      create: { name: "interactive" },
    }),
    prisma.tag.upsert({
      where: { name: "premium" },
      update: {},
      create: { name: "premium" },
    }),
    prisma.tag.upsert({
      where: { name: "exclusive" },
      update: {},
      create: { name: "exclusive" },
    }),
    prisma.tag.upsert({
      where: { name: "hd" },
      update: {},
      create: { name: "hd" },
    }),
    prisma.tag.upsert({
      where: { name: "new" },
      update: {},
      create: { name: "new" },
    }),
  ])

  console.log("✅ Tags created")

  // Create admin user
  const adminPassword = await hash("admin123", 12)
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      name: "Admin User",
      password: adminPassword,
      role: "admin",
      bio: "Platform administrator",
    },
  })

  console.log("✅ Admin user created")

  // Create sample creators
  const creatorPassword = await hash("creator123", 12)
  const creators = await Promise.all([
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Alice Creator",
        password: creatorPassword,
        role: "creator",
        bio: "Gaming enthusiast and live streamer. Join me for epic gaming sessions!",
        website: "https://alice-gaming.com",
        location: "Los Angeles, CA",
        followersCount: 1250,
        followingCount: 89,
        totalEarnings: 15000,
        availableBalance: 2500,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Bob Musician",
        password: creatorPassword,
        role: "creator",
        bio: "Professional musician sharing live performances and music creation process.",
        website: "https://bobmusic.com",
        location: "Nashville, TN",
        followersCount: 890,
        followingCount: 156,
        totalEarnings: 8500,
        availableBalance: 1200,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Carol Artist",
        password: creatorPassword,
        role: "creator",
        bio: "Digital artist creating beautiful artwork live. Watch the magic happen!",
        website: "https://carolart.com",
        location: "New York, NY",
        followersCount: 2100,
        followingCount: 234,
        totalEarnings: 22000,
        availableBalance: 3800,
      },
    }),
  ])

  console.log("✅ Sample creators created")

  // Create sample regular users
  const userPassword = await hash("user123", 12)
  const users = await Promise.all([
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "John Viewer",
        password: userPassword,
        role: "user",
        bio: "Love watching live streams and supporting creators!",
        followersCount: 45,
        followingCount: 12,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        email: "<EMAIL>",
        name: "Jane Fan",
        password: userPassword,
        role: "user",
        bio: "Art enthusiast and music lover. Always looking for new creators to follow.",
        followersCount: 78,
        followingCount: 23,
      },
    }),
  ])

  console.log("✅ Sample users created")

  // Create sample streams
  const streams = await Promise.all([
    prisma.stream.create({
      data: {
        title: "Epic Gaming Session - Live Now!",
        description: "Join me for some intense gaming action. Interactive chat and giveaways!",
        creatorId: creators[0].id,
        categoryId: categories[0].id,
        isLive: true,
        viewerCount: 45,
        totalViews: 1250,
        streamKey: "alice_stream_key_123",
        tags: {
          connect: [{ id: tags[0].id }, { id: tags[1].id }, { id: tags[4].id }],
        },
      },
    }),
    prisma.stream.create({
      data: {
        title: "Acoustic Guitar Session",
        description: "Relaxing acoustic guitar music. Taking requests from chat!",
        creatorId: creators[1].id,
        categoryId: categories[1].id,
        isLive: false,
        viewerCount: 0,
        totalViews: 890,
        streamKey: "bob_stream_key_456",
        tags: {
          connect: [{ id: tags[0].id }, { id: tags[1].id }],
        },
      },
    }),
    prisma.stream.create({
      data: {
        title: "Digital Art Creation Process",
        description: "Watch me create a digital masterpiece from scratch. Premium content!",
        creatorId: creators[2].id,
        categoryId: categories[2].id,
        isLive: true,
        viewerCount: 78,
        totalViews: 2100,
        streamKey: "carol_stream_key_789",
        tags: {
          connect: [{ id: tags[0].id }, { id: tags[2].id }, { id: tags[3].id }],
        },
      },
    }),
  ])

  console.log("✅ Sample streams created")

  // Create sample follows
  await Promise.all([
    prisma.follow.create({
      data: {
        followerId: users[0].id,
        followingId: creators[0].id,
      },
    }),
    prisma.follow.create({
      data: {
        followerId: users[0].id,
        followingId: creators[2].id,
      },
    }),
    prisma.follow.create({
      data: {
        followerId: users[1].id,
        followingId: creators[1].id,
      },
    }),
    prisma.follow.create({
      data: {
        followerId: users[1].id,
        followingId: creators[2].id,
      },
    }),
  ])

  console.log("✅ Sample follows created")

  // Create sample donations
  await Promise.all([
    prisma.donation.create({
      data: {
        amount: 500,
        message: "Great stream! Keep it up!",
        userId: users[0].id,
        creatorId: creators[0].id,
        streamId: streams[0].id,
        status: "SUCCESS",
      },
    }),
    prisma.donation.create({
      data: {
        amount: 1000,
        message: "Love your music!",
        userId: users[1].id,
        creatorId: creators[1].id,
        status: "SUCCESS",
      },
    }),
    prisma.donation.create({
      data: {
        amount: 750,
        message: "Amazing artwork!",
        userId: users[0].id,
        creatorId: creators[2].id,
        streamId: streams[2].id,
        status: "SUCCESS",
      },
    }),
  ])

  console.log("✅ Sample donations created")

  // Create sample subscriptions
  await Promise.all([
    prisma.subscription.create({
      data: {
        userId: users[0].id,
        creatorId: creators[0].id,
        tier: "PREMIUM",
        amount: 299,
        status: "ACTIVE",
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
    }),
    prisma.subscription.create({
      data: {
        userId: users[1].id,
        creatorId: creators[2].id,
        tier: "VIP",
        amount: 599,
        status: "ACTIVE",
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
    }),
  ])

  console.log("✅ Sample subscriptions created")

  console.log("🎉 Database seeding completed successfully!")
  console.log("\n📋 Seeded data summary:")
  console.log(`- ${categories.length} categories`)
  console.log(`- ${tags.length} tags`)
  console.log(`- 1 admin user`)
  console.log(`- ${creators.length} creator users`)
  console.log(`- ${users.length} regular users`)
  console.log(`- ${streams.length} streams`)
  console.log("- 4 follow relationships")
  console.log("- 3 donations")
  console.log("- 2 subscriptions")
  console.log("\n🔑 Login credentials:")
  console.log("Admin: <EMAIL> / admin123")
  console.log("Creator: <EMAIL> / creator123")
  console.log("User: <EMAIL> / user123")
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
