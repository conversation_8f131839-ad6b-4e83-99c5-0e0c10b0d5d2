import { NextResponse } from "next/server"
import { logger } from "@/lib/logger"
import { Prisma } from "@prisma/client"
import { ZodError } from "zod"

export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean
  public code?: string

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.code = code

    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, "VALIDATION_ERROR")
    this.name = "ValidationError"
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = "Authentication required") {
    super(message, 401, true, "AUTHENTICATION_ERROR")
    this.name = "AuthenticationError"
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = "Insufficient permissions") {
    super(message, 403, true, "AUTHORIZATION_ERROR")
    this.name = "AuthorizationError"
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = "Resource") {
    super(`${resource} not found`, 404, true, "NOT_FOUND_ERROR")
    this.name = "NotFoundError"
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, "CONFLICT_ERROR")
    this.name = "ConflictError"
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = "Rate limit exceeded") {
    super(message, 429, true, "RATE_LIMIT_ERROR")
    this.name = "RateLimitError"
  }
}

export class PaymentError extends AppError {
  constructor(message: string) {
    super(message, 402, true, "PAYMENT_ERROR")
    this.name = "PaymentError"
  }
}

export class ContentModerationError extends AppError {
  constructor(message: string) {
    super(message, 451, true, "CONTENT_MODERATION_ERROR")
    this.name = "ContentModerationError"
  }
}

// Error handler for API routes
export function handleApiError(error: unknown, req?: Request): NextResponse {
  let statusCode = 500
  let message = "Internal server error"
  let code = "INTERNAL_ERROR"
  let details: any = undefined

  // Log the error
  logger.error("API Error", {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    url: req?.url,
    method: req?.method,
  })

  if (error instanceof AppError) {
    statusCode = error.statusCode
    message = error.message
    code = error.code || "APP_ERROR"
  } else if (error instanceof ZodError) {
    statusCode = 400
    message = "Validation failed"
    code = "VALIDATION_ERROR"
    details = error.errors.map(err => ({
      field: err.path.join("."),
      message: err.message,
    }))
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case "P2002":
        statusCode = 409
        message = "Resource already exists"
        code = "DUPLICATE_ERROR"
        break
      case "P2025":
        statusCode = 404
        message = "Resource not found"
        code = "NOT_FOUND_ERROR"
        break
      case "P2003":
        statusCode = 400
        message = "Foreign key constraint failed"
        code = "CONSTRAINT_ERROR"
        break
      default:
        statusCode = 500
        message = "Database error"
        code = "DATABASE_ERROR"
    }
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = 400
    message = "Invalid data provided"
    code = "VALIDATION_ERROR"
  } else if (error instanceof Error) {
    message = process.env.NODE_ENV === "development" ? error.message : "Internal server error"
  }

  const errorResponse = {
    error: {
      message,
      code,
      statusCode,
      ...(details && { details }),
      ...(process.env.NODE_ENV === "development" && {
        stack: error instanceof Error ? error.stack : undefined,
      }),
    },
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
  }

  return NextResponse.json(errorResponse, { status: statusCode })
}

// Async error wrapper for API routes
export function asyncHandler(fn: Function) {
  return async (req: Request, ...args: any[]) => {
    try {
      return await fn(req, ...args)
    } catch (error) {
      return handleApiError(error, req)
    }
  }
}

// Global error handler for unhandled errors
export function setupGlobalErrorHandlers() {
  process.on("uncaughtException", (error: Error) => {
    logger.error("Uncaught Exception", {
      error: error.message,
      stack: error.stack,
    })
    
    // Graceful shutdown
    process.exit(1)
  })

  process.on("unhandledRejection", (reason: any, promise: Promise<any>) => {
    logger.error("Unhandled Rejection", {
      reason: reason instanceof Error ? reason.message : String(reason),
      stack: reason instanceof Error ? reason.stack : undefined,
    })
    
    // Don't exit the process for unhandled rejections in production
    if (process.env.NODE_ENV !== "production") {
      process.exit(1)
    }
  })
}

// Error boundary for React components
export class ErrorBoundary extends Error {
  constructor(message: string, public componentStack?: string) {
    super(message)
    this.name = "ErrorBoundary"
  }
}

// Utility functions for error handling
export const errorUtils = {
  // Check if error is operational (safe to show to user)
  isOperationalError: (error: unknown): boolean => {
    if (error instanceof AppError) {
      return error.isOperational
    }
    return false
  },

  // Extract error message safely
  getErrorMessage: (error: unknown): string => {
    if (error instanceof Error) {
      return error.message
    }
    return String(error)
  },

  // Create error response for client
  createErrorResponse: (error: unknown, includeStack: boolean = false) => {
    const message = errorUtils.getErrorMessage(error)
    const response: any = { message }

    if (error instanceof AppError) {
      response.code = error.code
      response.statusCode = error.statusCode
    }

    if (includeStack && error instanceof Error) {
      response.stack = error.stack
    }

    return response
  },

  // Log error with context
  logError: (error: unknown, context?: Record<string, any>) => {
    logger.error("Application Error", {
      error: errorUtils.getErrorMessage(error),
      stack: error instanceof Error ? error.stack : undefined,
      ...context,
    })
  },
}

// Initialize global error handlers
if (typeof window === "undefined") {
  setupGlobalErrorHandlers()
}
