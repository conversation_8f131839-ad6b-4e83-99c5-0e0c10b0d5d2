import { Cashfree } from "cashfree-pg"

// Initialize Cashfree
Cashfree.XClientId = process.env.CASHFREE_APP_ID!
Cashfree.XClientSecret = process.env.CASHFREE_SECRET_KEY!
Cashfree.XEnvironment =
  process.env.NODE_ENV === "production" ? Cashfree.Environment.PRODUCTION : Cashfree.Environment.SANDBOX

export { Cashfree }

export interface CreateOrderRequest {
  order_amount: number
  order_currency: string
  order_id: string
  customer_details: {
    customer_id: string
    customer_name: string
    customer_email: string
    customer_phone: string
  }
  order_meta?: {
    return_url?: string
    notify_url?: string
  }
}

export interface PayoutRequest {
  payout_id: string
  amount: number
  beneficiary: {
    name: string
    email: string
    phone: string
    bank_account: string
    ifsc: string
  }
}
