import { Server as SocketIOServer } from "socket.io"
import { logger } from "@/lib/logger"

interface StreamSession {
  streamId: string
  creatorId: string
  viewers: Set<string>
  isLive: boolean
}

class WebRTCStreamingServer {
  private io: SocketIOServer
  private sessions: Map<string, StreamSession> = new Map()

  constructor(io: SocketIOServer) {
    this.io = io
    this.setupSocketHandlers()
  }

  private setupSocketHandlers() {
    this.io.on("connection", (socket) => {
      logger.info("WebRTC client connected", { socketId: socket.id })

      // Creator starts streaming
      socket.on("start-stream", async (data: { streamId: string, creatorId: string }) => {
        try {
          const { streamId, creatorId } = data
          
          // Create or update stream session
          const session: StreamSession = {
            streamId,
            creatorId,
            viewers: new Set(),
            isLive: true,
          }
          
          this.sessions.set(streamId, session)
          socket.join(`stream-${streamId}`)
          
          // Notify all viewers that stream is live
          socket.to(`stream-${streamId}`).emit("stream-started", { streamId })
          
          logger.info("WebRTC stream started", { streamId, creatorId })
        } catch (error) {
          logger.error("Error starting WebRTC stream", { error })
          socket.emit("stream-error", { error: "Failed to start stream" })
        }
      })

      // Creator stops streaming
      socket.on("stop-stream", (data: { streamId: string }) => {
        try {
          const { streamId } = data
          const session = this.sessions.get(streamId)
          
          if (session) {
            session.isLive = false
            socket.to(`stream-${streamId}`).emit("stream-ended", { streamId })
            this.sessions.delete(streamId)
          }
          
          logger.info("WebRTC stream stopped", { streamId })
        } catch (error) {
          logger.error("Error stopping WebRTC stream", { error })
        }
      })

      // Viewer joins stream
      socket.on("join-stream", (data: { streamId: string, userId?: string }) => {
        try {
          const { streamId, userId } = data
          const session = this.sessions.get(streamId)
          
          if (session && session.isLive) {
            socket.join(`stream-${streamId}`)
            
            if (userId) {
              session.viewers.add(userId)
            }
            
            // Send current viewer count to all viewers
            this.io.to(`stream-${streamId}`).emit("viewer-count", {
              count: session.viewers.size,
            })
            
            socket.emit("stream-joined", { streamId })
            logger.info("Viewer joined stream", { streamId, userId, viewerCount: session.viewers.size })
          } else {
            socket.emit("stream-error", { error: "Stream not found or offline" })
          }
        } catch (error) {
          logger.error("Error joining stream", { error })
        }
      })

      // Viewer leaves stream
      socket.on("leave-stream", (data: { streamId: string, userId?: string }) => {
        try {
          const { streamId, userId } = data
          const session = this.sessions.get(streamId)
          
          if (session && userId) {
            session.viewers.delete(userId)
            
            // Send updated viewer count
            this.io.to(`stream-${streamId}`).emit("viewer-count", {
              count: session.viewers.size,
            })
          }
          
          socket.leave(`stream-${streamId}`)
          logger.info("Viewer left stream", { streamId, userId })
        } catch (error) {
          logger.error("Error leaving stream", { error })
        }
      })

      // WebRTC signaling
      socket.on("offer", (data: { streamId: string, offer: any, targetId?: string }) => {
        const { streamId, offer, targetId } = data
        
        if (targetId) {
          socket.to(targetId).emit("offer", { offer, senderId: socket.id })
        } else {
          socket.to(`stream-${streamId}`).emit("offer", { offer, senderId: socket.id })
        }
      })

      socket.on("answer", (data: { answer: any, targetId: string }) => {
        const { answer, targetId } = data
        socket.to(targetId).emit("answer", { answer, senderId: socket.id })
      })

      socket.on("ice-candidate", (data: { candidate: any, targetId?: string, streamId?: string }) => {
        const { candidate, targetId, streamId } = data
        
        if (targetId) {
          socket.to(targetId).emit("ice-candidate", { candidate, senderId: socket.id })
        } else if (streamId) {
          socket.to(`stream-${streamId}`).emit("ice-candidate", { candidate, senderId: socket.id })
        }
      })

      // Handle disconnection
      socket.on("disconnect", () => {
        logger.info("WebRTC client disconnected", { socketId: socket.id })
        
        // Remove from all stream sessions
        for (const [streamId, session] of this.sessions.entries()) {
          if (session.viewers.has(socket.id)) {
            session.viewers.delete(socket.id)
            
            // Update viewer count
            this.io.to(`stream-${streamId}`).emit("viewer-count", {
              count: session.viewers.size,
            })
          }
        }
      })
    })
  }

  getActiveStreams() {
    const activeStreams = Array.from(this.sessions.entries()).map(([streamId, session]) => ({
      streamId,
      creatorId: session.creatorId,
      viewerCount: session.viewers.size,
      isLive: session.isLive,
    }))
    
    return activeStreams
  }

  getStreamStats(streamId: string) {
    const session = this.sessions.get(streamId)
    
    if (!session) {
      return null
    }
    
    return {
      streamId,
      creatorId: session.creatorId,
      viewerCount: session.viewers.size,
      isLive: session.isLive,
      viewers: Array.from(session.viewers),
    }
  }
}

export default WebRTCStreamingServer
