import NodeMediaServer from "node-media-server"
import prisma from "@/lib/prisma"
import { logger } from "@/lib/logger"

const config = {
  rtmp: {
    port: 1935,
    chunk_size: 60000,
    gop_cache: true,
    ping: 30,
    ping_timeout: 60,
  },
  http: {
    port: 8000,
    mediaroot: "./media",
    allow_origin: "*",
  },
  relay: {
    ffmpeg: "/usr/local/bin/ffmpeg",
    tasks: [
      {
        app: "live",
        mode: "push",
        edge: "rtmp://127.0.0.1/live_hls",
      },
    ],
  },
  auth: {
    play: false,
    publish: true,
    secret: process.env.RTMP_SECRET || "supersecret",
  },
}

class StreamingServer {
  private nms: any

  constructor() {
    this.nms = new NodeMediaServer(config)
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.nms.on("preConnect", (id: string, args: any) => {
      logger.info("RTMP connection attempt", { id, args })
    })

    this.nms.on("postConnect", (id: string, args: any) => {
      logger.info("RTMP connected", { id, args })
    })

    this.nms.on("doneConnect", (id: string, args: any) => {
      logger.info("RTMP disconnected", { id, args })
    })

    this.nms.on("prePublish", async (id: string, StreamPath: string, args: any) => {
      logger.info("Stream publish attempt", { id, StreamPath, args })
      
      try {
        // Extract stream key from path (format: /live/STREAM_KEY)
        const streamKey = StreamPath.split("/").pop()
        
        if (!streamKey) {
          logger.error("No stream key provided")
          return this.nms.getSession(id).reject()
        }

        // Verify stream key exists and belongs to a user
        const stream = await prisma.stream.findUnique({
          where: { streamKey },
          include: { creator: true },
        })

        if (!stream) {
          logger.error("Invalid stream key", { streamKey })
          return this.nms.getSession(id).reject()
        }

        // Update stream status to live
        await prisma.stream.update({
          where: { id: stream.id },
          data: {
            isLive: true,
            startTime: new Date(),
            rtmpUrl: `rtmp://localhost:1935/live/${streamKey}`,
            hlsUrl: `http://localhost:8000/live/${streamKey}/index.m3u8`,
          },
        })

        logger.info("Stream started", { streamId: stream.id, creator: stream.creator.name })
      } catch (error) {
        logger.error("Error during stream publish", { error })
        return this.nms.getSession(id).reject()
      }
    })

    this.nms.on("postPublish", (id: string, StreamPath: string, args: any) => {
      logger.info("Stream published", { id, StreamPath, args })
    })

    this.nms.on("donePublish", async (id: string, StreamPath: string, args: any) => {
      logger.info("Stream ended", { id, StreamPath, args })
      
      try {
        const streamKey = StreamPath.split("/").pop()
        
        if (streamKey) {
          // Update stream status to offline
          await prisma.stream.updateMany({
            where: { streamKey },
            data: {
              isLive: false,
              endTime: new Date(),
            },
          })
          
          logger.info("Stream marked as offline", { streamKey })
        }
      } catch (error) {
        logger.error("Error updating stream status", { error })
      }
    })

    this.nms.on("prePlay", (id: string, StreamPath: string, args: any) => {
      logger.info("Stream play attempt", { id, StreamPath, args })
    })

    this.nms.on("postPlay", (id: string, StreamPath: string, args: any) => {
      logger.info("Stream playing", { id, StreamPath, args })
    })

    this.nms.on("donePlay", (id: string, StreamPath: string, args: any) => {
      logger.info("Stream play ended", { id, StreamPath, args })
    })
  }

  start() {
    this.nms.run()
    logger.info("RTMP server started", { 
      rtmpPort: config.rtmp.port, 
      httpPort: config.http.port 
    })
  }

  stop() {
    this.nms.stop()
    logger.info("RTMP server stopped")
  }

  getStats() {
    return this.nms.getStats()
  }
}

export default StreamingServer
