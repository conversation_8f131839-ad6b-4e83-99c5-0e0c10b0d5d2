"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export function useAuth(requiredRole?: string) {
  const { data: session, status } = useSession()
  const loading = status === "loading"
  const router = useRouter()

  useEffect(() => {
    if (!loading && !session) {
      router.push("/api/auth/signin")
    } else if (session && requiredRole && session.user.role !== requiredRole) {
      router.push("/unauthorized")
    }
  }, [loading, session, requiredRole, router])

  return { session, loading }
}
