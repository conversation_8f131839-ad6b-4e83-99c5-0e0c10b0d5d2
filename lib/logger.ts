import pino from "pino"
import path from "path"
import fs from "fs"

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), "logs")
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true })
}

// Create base logger
const baseLogger = pino({
  level: process.env.LOG_LEVEL || "info",
  transport: process.env.NODE_ENV === "development" ? {
    target: "pino-pretty",
    options: {
      colorize: true,
      translateTime: "yyyy-mm-dd HH:MM:ss",
      ignore: "pid,hostname",
    },
  } : undefined,
  formatters: {
    level: (label) => {
      return { level: label }
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
})

// Enhanced logger with additional methods
export const logger = {
  ...baseLogger,

  // Security-specific logging
  security: (event: string, details: any) => {
    baseLogger.warn({
      type: "security",
      event,
      ...details,
    }, `SECURITY: ${event}`)
  },

  // Performance logging
  performance: (operation: string, duration: number, meta?: any) => {
    baseLogger.info({
      type: "performance",
      operation,
      duration,
      ...meta,
    }, `PERFORMANCE: ${operation} took ${duration}ms`)
  },

  // Business logic logging
  business: (event: string, details: any) => {
    baseLogger.info({
      type: "business",
      event,
      ...details,
    }, `BUSINESS: ${event}`)
  },

  // Audit logging
  audit: (action: string, userId: string, details: any) => {
    baseLogger.info({
      type: "audit",
      action,
      userId,
      ...details,
    }, `AUDIT: ${action} by user ${userId}`)
  },

  // API request logging
  request: (method: string, url: string, statusCode: number, duration: number, meta?: any) => {
    baseLogger.info({
      type: "request",
      method,
      url,
      statusCode,
      duration,
      ...meta,
    }, `${method} ${url} ${statusCode} - ${duration}ms`)
  },

  // Database operation logging
  database: (operation: string, table: string, duration: number, meta?: any) => {
    baseLogger.debug({
      type: "database",
      operation,
      table,
      duration,
      ...meta,
    }, `DB: ${operation} on ${table} took ${duration}ms`)
  },
}
