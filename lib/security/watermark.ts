import sharp from "sharp"
import { createCanvas, loadImage } from "canvas"
import { logger } from "@/lib/logger"

interface WatermarkOptions {
  text?: string
  opacity?: number
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right" | "center"
  fontSize?: number
  color?: string
  logoPath?: string
}

export class ContentWatermark {
  private defaultOptions: WatermarkOptions = {
    text: "FansXcluziv.com",
    opacity: 0.3,
    position: "bottom-right",
    fontSize: 24,
    color: "white",
  }

  async addImageWatermark(
    imageBuffer: <PERSON>uffer,
    options: WatermarkOptions = {}
  ): Promise<Buffer> {
    try {
      const opts = { ...this.defaultOptions, ...options }
      const image = sharp(imageBuffer)
      const { width, height } = await image.metadata()

      if (!width || !height) {
        throw new Error("Invalid image dimensions")
      }

      // Create watermark SVG
      const watermarkSvg = this.createWatermarkSVG(opts, width, height)
      
      // Apply watermark
      const watermarkedImage = await image
        .composite([
          {
            input: Buffer.from(watermarkSvg),
            gravity: this.getGravity(opts.position!),
          },
        ])
        .jpeg({ quality: 90 })
        .toBuffer()

      logger.info("Image watermark applied successfully")
      return watermarkedImage
    } catch (error) {
      logger.error("Error applying image watermark:", error)
      throw error
    }
  }

  async addVideoWatermark(
    videoPath: string,
    outputPath: string,
    options: WatermarkOptions = {}
  ): Promise<string> {
    try {
      const opts = { ...this.defaultOptions, ...options }
      const ffmpeg = require("fluent-ffmpeg")

      return new Promise((resolve, reject) => {
        const watermarkText = opts.text || "FansXcluziv.com"
        const position = this.getVideoPosition(opts.position!)
        
        ffmpeg(videoPath)
          .videoFilters([
            {
              filter: "drawtext",
              options: {
                text: watermarkText,
                fontsize: opts.fontSize,
                fontcolor: opts.color,
                alpha: opts.opacity,
                x: position.x,
                y: position.y,
              },
            },
          ])
          .output(outputPath)
          .on("end", () => {
            logger.info("Video watermark applied successfully")
            resolve(outputPath)
          })
          .on("error", (error: Error) => {
            logger.error("Error applying video watermark:", error)
            reject(error)
          })
          .run()
      })
    } catch (error) {
      logger.error("Error setting up video watermark:", error)
      throw error
    }
  }

  async addLiveStreamWatermark(
    streamUrl: string,
    outputUrl: string,
    options: WatermarkOptions = {}
  ): Promise<void> {
    try {
      const opts = { ...this.defaultOptions, ...options }
      const ffmpeg = require("fluent-ffmpeg")

      const watermarkText = opts.text || "FansXcluziv.com"
      const position = this.getVideoPosition(opts.position!)

      ffmpeg(streamUrl)
        .videoFilters([
          {
            filter: "drawtext",
            options: {
              text: watermarkText,
              fontsize: opts.fontSize,
              fontcolor: opts.color,
              alpha: opts.opacity,
              x: position.x,
              y: position.y,
            },
          },
        ])
        .outputOptions([
          "-f", "flv",
          "-c:v", "libx264",
          "-c:a", "aac",
          "-preset", "fast",
          "-tune", "zerolatency",
        ])
        .output(outputUrl)
        .on("start", () => {
          logger.info("Live stream watermarking started")
        })
        .on("error", (error: Error) => {
          logger.error("Live stream watermarking error:", error)
        })
        .run()
    } catch (error) {
      logger.error("Error setting up live stream watermark:", error)
      throw error
    }
  }

  private createWatermarkSVG(
    options: WatermarkOptions,
    imageWidth: number,
    imageHeight: number
  ): string {
    const { text, fontSize, color, opacity } = options
    const padding = 20

    let x: number, y: number

    switch (options.position) {
      case "top-left":
        x = padding
        y = padding + fontSize!
        break
      case "top-right":
        x = imageWidth - padding
        y = padding + fontSize!
        break
      case "bottom-left":
        x = padding
        y = imageHeight - padding
        break
      case "bottom-right":
        x = imageWidth - padding
        y = imageHeight - padding
        break
      case "center":
        x = imageWidth / 2
        y = imageHeight / 2
        break
      default:
        x = imageWidth - padding
        y = imageHeight - padding
    }

    const textAnchor = options.position?.includes("right") ? "end" : "start"

    return `
      <svg width="${imageWidth}" height="${imageHeight}">
        <text
          x="${x}"
          y="${y}"
          font-family="Arial, sans-serif"
          font-size="${fontSize}"
          fill="${color}"
          opacity="${opacity}"
          text-anchor="${textAnchor}"
        >${text}</text>
      </svg>
    `
  }

  private getGravity(position: string): string {
    switch (position) {
      case "top-left":
        return "northwest"
      case "top-right":
        return "northeast"
      case "bottom-left":
        return "southwest"
      case "bottom-right":
        return "southeast"
      case "center":
        return "center"
      default:
        return "southeast"
    }
  }

  private getVideoPosition(position: string): { x: string; y: string } {
    switch (position) {
      case "top-left":
        return { x: "20", y: "20" }
      case "top-right":
        return { x: "w-tw-20", y: "20" }
      case "bottom-left":
        return { x: "20", y: "h-th-20" }
      case "bottom-right":
        return { x: "w-tw-20", y: "h-th-20" }
      case "center":
        return { x: "(w-tw)/2", y: "(h-th)/2" }
      default:
        return { x: "w-tw-20", y: "h-th-20" }
    }
  }

  // Generate unique watermark for each user/content
  generateUniqueWatermark(userId: string, contentId: string): string {
    const timestamp = Date.now()
    const hash = require("crypto")
      .createHash("md5")
      .update(`${userId}-${contentId}-${timestamp}`)
      .digest("hex")
      .substring(0, 8)
    
    return `FansXcluziv.com • ${hash}`
  }

  // Invisible watermark for advanced protection
  async addInvisibleWatermark(
    imageBuffer: Buffer,
    watermarkData: string
  ): Promise<Buffer> {
    try {
      // This is a simplified version - in production, you'd use more sophisticated steganography
      const image = sharp(imageBuffer)
      const { width, height } = await image.metadata()

      if (!width || !height) {
        throw new Error("Invalid image dimensions")
      }

      // Embed watermark data in image metadata
      const watermarkedImage = await image
        .withMetadata({
          exif: {
            IFD0: {
              Copyright: watermarkData,
              Software: "FansXcluziv Watermark System",
            },
          },
        })
        .jpeg({ quality: 95 })
        .toBuffer()

      return watermarkedImage
    } catch (error) {
      logger.error("Error applying invisible watermark:", error)
      throw error
    }
  }
}

export const watermarkService = new ContentWatermark()
