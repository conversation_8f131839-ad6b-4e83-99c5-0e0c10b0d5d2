import type { Server as NetServer } from "http"
import type { NextApiRequest } from "next"
import { Server as ServerIO } from "socket.io"
import prisma from "@/lib/prisma"
import WebRTCStreamingServer from "@/lib/streaming/webrtc-server"
import { logger } from "@/lib/logger"

export type NextApiResponseServerIO = {
  socket: {
    server: NetServer & {
      io: ServerIO
    }
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}

const SocketHandler = (req: NextApiRequest, res: NextApiResponseServerIO) => {
  if (res.socket.server.io) {
    console.log("Socket is already running")
  } else {
    console.log("Socket is initializing")
    const io = new ServerIO(res.socket.server, {
      cors: {
        origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
      },
    })
    res.socket.server.io = io

    // Initialize WebRTC streaming server
    const webrtcServer = new WebRTCStreamingServer(io)

    // Enhanced chat functionality
    io.on("connection", (socket) => {
      logger.info("Client connected", { socketId: socket.id })

      // Join stream room for chat
      socket.on("join-stream", async (streamId: string) => {
        try {
          socket.join(`stream-${streamId}`)

          // Get recent chat messages
          const recentMessages = await prisma.chatMessage.findMany({
            where: { streamId },
            include: {
              user: {
                select: { id: true, name: true, image: true },
              },
            },
            orderBy: { createdAt: "desc" },
            take: 50,
          })

          socket.emit("chat-history", recentMessages.reverse())
          logger.info("User joined stream chat", { socketId: socket.id, streamId })
        } catch (error) {
          logger.error("Error joining stream", { error })
        }
      })

      // Leave stream room
      socket.on("leave-stream", (streamId: string) => {
        socket.leave(`stream-${streamId}`)
        logger.info("User left stream chat", { socketId: socket.id, streamId })
      })

      // Send chat message
      socket.on("send-message", async (data: { streamId: string, content: string, userId: string }) => {
        try {
          const { streamId, content, userId } = data

          // Create message in database
          const message = await prisma.chatMessage.create({
            data: {
              content,
              userId,
              streamId,
            },
            include: {
              user: {
                select: { id: true, name: true, image: true },
              },
            },
          })

          // Broadcast to all users in the stream
          io.to(`stream-${streamId}`).emit("new-message", message)
          logger.info("Chat message sent", { streamId, userId, messageId: message.id })
        } catch (error) {
          logger.error("Error sending message", { error })
          socket.emit("message-error", { error: "Failed to send message" })
        }
      })

      // Handle donations in real-time
      socket.on("donation-alert", (data: { streamId: string, donation: any }) => {
        const { streamId, donation } = data
        io.to(`stream-${streamId}`).emit("donation-received", donation)
        logger.info("Donation alert sent", { streamId, donationId: donation.id })
      })

      // Handle disconnection
      socket.on("disconnect", () => {
        logger.info("Client disconnected", { socketId: socket.id })
      })
    })

    io.on("connection", (socket) => {
      console.log("User connected:", socket.id)

      // Join stream room
      socket.on("join-stream", (streamId: string) => {
        socket.join(`stream-${streamId}`)
        console.log(`User ${socket.id} joined stream ${streamId}`)
      })

      // Leave stream room
      socket.on("leave-stream", (streamId: string) => {
        socket.leave(`stream-${streamId}`)
        console.log(`User ${socket.id} left stream ${streamId}`)
      })

      // Handle chat messages
      socket.on("send-message", async (data: { streamId: string; userId: string; content: string }) => {
        try {
          // Save message to database
          const message = await prisma.chatMessage.create({
            data: {
              content: data.content,
              userId: data.userId,
              streamId: data.streamId,
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          })

          // Broadcast message to all users in the stream room
          io.to(`stream-${data.streamId}`).emit("new-message", message)
        } catch (error) {
          console.error("Error saving chat message:", error)
        }
      })

      // Handle viewer count updates
      socket.on("update-viewer-count", async (streamId: string) => {
        try {
          const roomSize = io.sockets.adapter.rooms.get(`stream-${streamId}`)?.size || 0

          // Update viewer count in database
          await prisma.stream.update({
            where: { id: streamId },
            data: { viewerCount: roomSize },
          })

          // Broadcast updated viewer count
          io.to(`stream-${streamId}`).emit("viewer-count-updated", roomSize)
        } catch (error) {
          console.error("Error updating viewer count:", error)
        }
      })

      socket.on("disconnect", () => {
        console.log("User disconnected:", socket.id)
      })
    })
  }

  res.end()
}

export default SocketHandler
