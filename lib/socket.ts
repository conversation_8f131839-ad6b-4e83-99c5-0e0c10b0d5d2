import type { Server as NetServer } from "http"
import type { NextApiRequest } from "next"
import { Server as ServerIO } from "socket.io"
import { prisma } from "@/lib/prisma"

export type NextApiResponseServerIO = {
  socket: {
    server: NetServer & {
      io: ServerIO
    }
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}

const SocketHandler = (req: NextApiRequest, res: NextApiResponseServerIO) => {
  if (res.socket.server.io) {
    console.log("Socket is already running")
  } else {
    console.log("Socket is initializing")
    const io = new ServerIO(res.socket.server)
    res.socket.server.io = io

    io.on("connection", (socket) => {
      console.log("User connected:", socket.id)

      // Join stream room
      socket.on("join-stream", (streamId: string) => {
        socket.join(`stream-${streamId}`)
        console.log(`User ${socket.id} joined stream ${streamId}`)
      })

      // Leave stream room
      socket.on("leave-stream", (streamId: string) => {
        socket.leave(`stream-${streamId}`)
        console.log(`User ${socket.id} left stream ${streamId}`)
      })

      // Handle chat messages
      socket.on("send-message", async (data: { streamId: string; userId: string; content: string }) => {
        try {
          // Save message to database
          const message = await prisma.chatMessage.create({
            data: {
              content: data.content,
              userId: data.userId,
              streamId: data.streamId,
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          })

          // Broadcast message to all users in the stream room
          io.to(`stream-${data.streamId}`).emit("new-message", message)
        } catch (error) {
          console.error("Error saving chat message:", error)
        }
      })

      // Handle viewer count updates
      socket.on("update-viewer-count", async (streamId: string) => {
        try {
          const roomSize = io.sockets.adapter.rooms.get(`stream-${streamId}`)?.size || 0

          // Update viewer count in database
          await prisma.stream.update({
            where: { id: streamId },
            data: { viewerCount: roomSize },
          })

          // Broadcast updated viewer count
          io.to(`stream-${streamId}`).emit("viewer-count-updated", roomSize)
        } catch (error) {
          console.error("Error updating viewer count:", error)
        }
      })

      socket.on("disconnect", () => {
        console.log("User disconnected:", socket.id)
      })
    })
  }

  res.end()
}

export default SocketHandler
