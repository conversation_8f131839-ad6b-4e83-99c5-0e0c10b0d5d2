import { NextRequest, NextResponse } from "next/server"
import { rateLimit } from "./rateLimit"
import { authMiddleware } from "./auth"
import { validateRequest } from "./validation"
import { moderationService } from "@/lib/security/moderation"
import { logger } from "@/lib/logger"

// Security headers middleware
export function securityHeaders(response: NextResponse): NextResponse {
  // Content Security Policy
  response.headers.set(
    "Content-Security-Policy",
    [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://sdk.cashfree.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "media-src 'self' https: blob:",
      "connect-src 'self' https: wss: ws:",
      "frame-src 'self' https://sdk.cashfree.com",
    ].join("; ")
  )

  // Other security headers
  response.headers.set("X-Frame-Options", "DENY")
  response.headers.set("X-Content-Type-Options", "nosniff")
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin")
  response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()")
  response.headers.set("X-XSS-Protection", "1; mode=block")
  
  // HSTS (only in production with HTTPS)
  if (process.env.NODE_ENV === "production") {
    response.headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains; preload"
    )
  }

  return response
}

// CORS middleware
export function corsMiddleware(req: NextRequest, response: NextResponse): NextResponse {
  const origin = req.headers.get("origin")
  const allowedOrigins = [
    process.env.NEXTAUTH_URL,
    "http://localhost:3000",
    "https://fansxcluziv.com",
    // Add your production domains
  ].filter(Boolean)

  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set("Access-Control-Allow-Origin", origin)
  }

  response.headers.set("Access-Control-Allow-Credentials", "true")
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  )
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Requested-With, X-API-Key"
  )

  return response
}

// IP blocking middleware
const blockedIPs = new Set<string>()
const suspiciousIPs = new Map<string, { count: number; lastSeen: number }>()

export function ipSecurityMiddleware(req: NextRequest): NextResponse | null {
  const ip = getClientIP(req)
  
  // Check if IP is blocked
  if (blockedIPs.has(ip)) {
    logger.warn("Blocked IP attempted access", { ip })
    return NextResponse.json(
      { error: "Access denied" },
      { status: 403 }
    )
  }

  // Track suspicious activity
  const now = Date.now()
  const suspicious = suspiciousIPs.get(ip)
  
  if (suspicious) {
    // Reset count if more than 1 hour has passed
    if (now - suspicious.lastSeen > 60 * 60 * 1000) {
      suspiciousIPs.delete(ip)
    } else {
      suspicious.count++
      suspicious.lastSeen = now
      
      // Block IP if too many suspicious requests
      if (suspicious.count > 50) {
        blockedIPs.add(ip)
        logger.warn("IP blocked due to suspicious activity", { ip, count: suspicious.count })
        return NextResponse.json(
          { error: "Access denied" },
          { status: 403 }
        )
      }
    }
  }

  return null
}

function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get("x-forwarded-for")
  const realIP = req.headers.get("x-real-ip")
  
  if (forwarded) {
    return forwarded.split(",")[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return req.ip || "unknown"
}

// Request size limiting
export function requestSizeMiddleware(req: NextRequest, maxSize: number = 10 * 1024 * 1024): NextResponse | null {
  const contentLength = req.headers.get("content-length")
  
  if (contentLength && parseInt(contentLength) > maxSize) {
    return NextResponse.json(
      { error: "Request too large" },
      { status: 413 }
    )
  }
  
  return null
}

// Bot detection middleware
export function botDetectionMiddleware(req: NextRequest): NextResponse | null {
  const userAgent = req.headers.get("user-agent") || ""
  
  // Common bot patterns
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
  ]
  
  const isBot = botPatterns.some(pattern => pattern.test(userAgent))
  
  if (isBot) {
    const ip = getClientIP(req)
    
    // Track suspicious bot activity
    const suspicious = suspiciousIPs.get(ip) || { count: 0, lastSeen: Date.now() }
    suspicious.count++
    suspicious.lastSeen = Date.now()
    suspiciousIPs.set(ip, suspicious)
    
    logger.info("Bot detected", { ip, userAgent })
    
    // Allow legitimate bots but rate limit them more aggressively
    return rateLimit(req, {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10, // 10 requests per minute for bots
      message: "Bot rate limit exceeded",
    })
  }
  
  return null
}

// Honeypot middleware (trap for bots)
export function honeypotMiddleware(req: NextRequest): NextResponse | null {
  const honeypotPaths = [
    "/admin",
    "/wp-admin",
    "/administrator",
    "/.env",
    "/config",
    "/backup",
  ]
  
  const path = new URL(req.url).pathname
  
  if (honeypotPaths.some(honeypot => path.startsWith(honeypot))) {
    const ip = getClientIP(req)
    
    // Immediately block IPs that access honeypot paths
    blockedIPs.add(ip)
    
    logger.warn("Honeypot triggered - IP blocked", { ip, path })
    
    return NextResponse.json(
      { error: "Not found" },
      { status: 404 }
    )
  }
  
  return null
}

// Comprehensive security middleware
export async function securityMiddleware(req: NextRequest): Promise<NextResponse | null> {
  try {
    // Check honeypot first
    const honeypotResult = honeypotMiddleware(req)
    if (honeypotResult) return honeypotResult

    // IP security check
    const ipResult = ipSecurityMiddleware(req)
    if (ipResult) return ipResult

    // Bot detection
    const botResult = botDetectionMiddleware(req)
    if (botResult) return botResult

    // Request size limiting
    const sizeResult = requestSizeMiddleware(req)
    if (sizeResult) return sizeResult

    // Rate limiting
    const rateLimitResult = await rateLimit(req)
    if (rateLimitResult) return rateLimitResult

    return null // Continue processing
  } catch (error) {
    logger.error("Security middleware error:", error)
    return null // Allow request on error to prevent service disruption
  }
}

// Content moderation middleware for user-generated content
export async function contentModerationMiddleware(
  content: string,
  contentType: "text" | "image" | "video" = "text"
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    let result

    switch (contentType) {
      case "text":
        result = await moderationService.moderateText(content)
        break
      case "image":
        // For images, content would be a buffer, but we'll handle it as string for now
        result = await moderationService.moderateImage(Buffer.from(content, "base64"))
        break
      case "video":
        result = await moderationService.moderateVideo(content)
        break
      default:
        result = await moderationService.moderateText(content)
    }

    return {
      allowed: result.isAllowed,
      reason: result.reason,
    }
  } catch (error) {
    logger.error("Content moderation error:", error)
    return { allowed: true } // Allow content on error
  }
}
