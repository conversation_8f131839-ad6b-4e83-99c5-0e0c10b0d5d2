import type { NextApiRequest, NextApiResponse } from "next"
import { AppError } from "@/lib/error"
import { logger } from "@/lib/logger"

export function errorHandler(err: Error, req: NextApiRequest, res: NextApiResponse) {
  if (err instanceof AppError) {
    logger.error(`AppError: ${err.message}`, { status: err.statusCode, stack: err.stack })
    res.status(err.statusCode).json({ error: err.message })
  } else {
    logger.error(`Unhandled error: ${err.message}`, { stack: err.stack })
    res.status(500).json({ error: "Internal Server Error" })
  }
}
