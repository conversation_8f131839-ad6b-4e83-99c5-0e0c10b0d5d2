import { NextRequest, NextResponse } from "next/server"
import { z, ZodSchema } from "zod"
import DOMPurify from "isomorphic-dompurify"

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== "string") return input
  
  // Remove HTML tags and potentially dangerous content
  const sanitized = DOMPurify.sanitize(input, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  })
  
  // Additional sanitization
  return sanitized
    .trim()
    .replace(/[<>]/g, "") // Remove any remaining angle brackets
    .slice(0, 10000) // Limit length
}

// Content moderation
export function moderateContent(content: string): { isAllowed: boolean; reason?: string } {
  const bannedWords = [
    "spam", "scam", "fake", "fraud", "hack", "illegal",
    // Add more banned words as needed
  ]
  
  const suspiciousPatterns = [
    /\b(?:https?:\/\/)?(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}\b/g, // URLs
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card patterns
    /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, // Phone numbers
  ]
  
  const lowerContent = content.toLowerCase()
  
  // Check for banned words
  for (const word of bannedWords) {
    if (lowerContent.includes(word)) {
      return { isAllowed: false, reason: `Contains banned word: ${word}` }
    }
  }
  
  // Check for suspicious patterns
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(content)) {
      return { isAllowed: false, reason: "Contains suspicious content" }
    }
  }
  
  // Check for excessive caps
  const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length
  if (capsRatio > 0.7 && content.length > 10) {
    return { isAllowed: false, reason: "Excessive use of capital letters" }
  }
  
  return { isAllowed: true }
}

// Validation middleware factory
export function validateRequest<T>(schema: ZodSchema<T>) {
  return async (req: NextRequest): Promise<{ data: T; error?: never } | { data?: never; error: NextResponse }> => {
    try {
      const body = await req.json()
      
      // Sanitize string inputs
      const sanitizedBody = sanitizeObject(body)
      
      // Validate with Zod schema
      const validatedData = schema.parse(sanitizedBody)
      
      return { data: validatedData }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          error: NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors.map(err => ({
                field: err.path.join("."),
                message: err.message,
              }))
            },
            { status: 400 }
          )
        }
      }
      
      return {
        error: NextResponse.json(
          { error: "Invalid request body" },
          { status: 400 }
        )
      }
    }
  }
}

function sanitizeObject(obj: any): any {
  if (typeof obj === "string") {
    return sanitizeInput(obj)
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject)
  }
  
  if (obj && typeof obj === "object") {
    const sanitized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value)
    }
    return sanitized
  }
  
  return obj
}

// Common validation schemas
export const userRegistrationSchema = z.object({
  name: z.string().min(2).max(50),
  email: z.string().email(),
  password: z.string().min(8).max(128),
})

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
})

export const streamCreationSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  categoryId: z.string().optional(),
  tagIds: z.array(z.string()).optional(),
})

export const chatMessageSchema = z.object({
  content: z.string().min(1).max(500),
  streamId: z.string(),
})

export const donationSchema = z.object({
  amount: z.number().min(1).max(100000),
  message: z.string().max(200).optional(),
  creatorId: z.string(),
  streamId: z.string().optional(),
})

export const subscriptionSchema = z.object({
  creatorId: z.string(),
  tier: z.enum(["BASIC", "PREMIUM", "VIP"]),
})

// File upload validation
export function validateFileUpload(file: File, options: {
  maxSize?: number
  allowedTypes?: string[]
  allowedExtensions?: string[]
}): { isValid: boolean; error?: string } {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
    allowedExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"]
  } = options
  
  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`
    }
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed`
    }
  }
  
  // Check file extension
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
  if (!allowedExtensions.includes(extension)) {
    return {
      isValid: false,
      error: `File extension ${extension} is not allowed`
    }
  }
  
  return { isValid: true }
}

// SQL injection prevention (additional layer)
export function preventSQLInjection(input: string): boolean {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(--|\/\*|\*\/|;|'|"|`)/,
    /(\bOR\b|\bAND\b).*?[=<>]/i,
  ]
  
  return !sqlPatterns.some(pattern => pattern.test(input))
}

// XSS prevention
export function preventXSS(input: string): string {
  return input
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#x27;")
    .replace(/\//g, "&#x2F;")
}
