import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { rateLimit } from "@/lib/middleware/rateLimit"

export async function authMiddleware(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET })
  
  if (!token) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    )
  }

  return null // Continue to next middleware
}

export async function adminMiddleware(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET })
  
  if (!token) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    )
  }

  // Check if user has admin role (you'll need to add this to your user model)
  if (token.role !== "admin") {
    return NextResponse.json(
      { error: "Admin access required" },
      { status: 403 }
    )
  }

  return null
}

export async function creatorMiddleware(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET })
  
  if (!token) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    )
  }

  // Check if user is a creator or admin
  if (!["creator", "admin"].includes(token.role as string)) {
    return NextResponse.json(
      { error: "Creator access required" },
      { status: 403 }
    )
  }

  return null
}

export function withAuth(handler: Function, middleware: Function = authMiddleware) {
  return async (req: NextRequest, ...args: any[]) => {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(req)
    if (rateLimitResult) return rateLimitResult

    // Apply authentication middleware
    const authResult = await middleware(req)
    if (authResult) return authResult

    // Continue to the actual handler
    return handler(req, ...args)
  }
}

// CSRF Protection
export function generateCSRFToken(): string {
  return require("crypto").randomBytes(32).toString("hex")
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken
}

// API Key validation for external integrations
export function validateAPIKey(apiKey: string): boolean {
  const validKeys = process.env.API_KEYS?.split(",") || []
  return validKeys.includes(apiKey)
}

export async function apiKeyMiddleware(req: NextRequest) {
  const apiKey = req.headers.get("x-api-key")
  
  if (!apiKey || !validateAPIKey(apiKey)) {
    return NextResponse.json(
      { error: "Invalid API key" },
      { status: 401 }
    )
  }

  return null
}
