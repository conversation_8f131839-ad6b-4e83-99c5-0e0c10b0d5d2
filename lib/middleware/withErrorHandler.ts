import type { NextApiRequest, NextApiResponse } from "next"
import { AppError, createErrorResponse, isOperationalError } from "@/lib/error"
import { logger } from "@/lib/logger"

type ApiHandler = (req: NextApiRequest, res: NextApiResponse) => Promise<void> | void

export function with<PERSON>rror<PERSON>and<PERSON>(handler: ApiHandler) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      await handler(req, res)
    } catch (error) {
      handleError(error, req, res)
    }
  }
}

function handleError(error: unknown, req: NextApiRequest, res: NextApiResponse) {
  // Log the error
  if (error instanceof Error) {
    logger.error(`API Error: ${error.message}`, {
      method: req.method,
      url: req.url,
      stack: error.stack,
      operational: isOperationalError(error),
    })
  } else {
    logger.error("Unknown API Error", {
      method: req.method,
      url: req.url,
      error: String(error),
    })
  }

  // Send error response
  if (error instanceof AppError) {
    const errorResponse = createErrorResponse(error)
    res.status(error.statusCode).json(errorResponse)
  } else if (error instanceof Error) {
    // Handle known error types
    if (error.name === "ValidationError") {
      res.status(400).json(createErrorResponse(new AppError(error.message, 400)))
    } else if (error.name === "CastError") {
      res.status(400).json(createErrorResponse(new AppError("Invalid ID format", 400)))
    } else {
      // Unknown error - don't expose details
      res.status(500).json(createErrorResponse(new AppError("Internal Server Error", 500)))
    }
  } else {
    // Completely unknown error
    res.status(500).json(createErrorResponse(new AppError("Internal Server Error", 500)))
  }
}
