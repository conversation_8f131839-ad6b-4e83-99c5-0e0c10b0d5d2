import type { NextApiRequest, NextApiResponse } from "next"
import rateLimit from "express-rate-limit"
import { AppError } from "@/lib/error"

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP, please try again later.",
  handler: (req, res) => {
    throw new AppError("Rate limit exceeded", 429)
  },
})

export function rateLimitMiddleware(req: NextApiRequest, res: NextApiResponse) {
  return new Promise((resolve, reject) => {
    limiter(req, res, (result) => {
      if (result instanceof Error) {
        return reject(result)
      }
      return resolve(result)
    })
  })
}
