import { NextRequest, NextResponse } from "next/server"
import { Redis } from "ioredis"

const redis = new Redis(process.env.REDIS_URL || "redis://localhost:6379")

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  message?: string
  skipSuccessfulRequests?: boolean
}

const defaultConfig: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per 15 minutes
  message: "Too many requests, please try again later",
}

export async function rateLimit(
  req: NextRequest,
  config: RateLimitConfig = defaultConfig
): Promise<NextResponse | null> {
  try {
    const ip = getClientIP(req)
    const key = `rate_limit:${ip}`

    const current = await redis.get(key)
    const currentCount = current ? parseInt(current) : 0

    if (currentCount >= config.maxRequests) {
      return NextResponse.json(
        {
          error: config.message,
          retryAfter: Math.ceil(config.windowMs / 1000)
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': new Date(Date.now() + config.windowMs).toISOString(),
          }
        }
      )
    }

    // Increment counter
    const newCount = currentCount + 1
    await redis.setex(key, Math.ceil(config.windowMs / 1000), newCount)

    return null // Continue processing
  } catch (error) {
    console.error("Rate limiting error:", error)
    return null // Allow request on error
  }
}

// Specific rate limiters for different endpoints
export const authRateLimit = (req: NextRequest) => rateLimit(req, {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 login attempts per 15 minutes
  message: "Too many authentication attempts, please try again later",
})

export const apiRateLimit = (req: NextRequest) => rateLimit(req, {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 60, // 60 requests per minute
  message: "API rate limit exceeded",
})

export const chatRateLimit = (req: NextRequest) => rateLimit(req, {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 30, // 30 messages per minute
  message: "Chat rate limit exceeded",
})

export const donationRateLimit = (req: NextRequest) => rateLimit(req, {
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 5, // 5 donations per minute
  message: "Donation rate limit exceeded",
})

function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get("x-forwarded-for")
  const realIP = req.headers.get("x-real-ip")

  if (forwarded) {
    return forwarded.split(",")[0].trim()
  }

  if (realIP) {
    return realIP
  }

  return req.ip || "unknown"
}
