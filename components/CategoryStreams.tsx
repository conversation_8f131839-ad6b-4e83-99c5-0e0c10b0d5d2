import type React from "react"
import { useState, useEffect } from "react"
import type { Stream, Category, Tag } from "@prisma/client"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectGroup,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface CategoryStreamsProps {
  category: Category
}

export const CategoryStreams: React.FC<CategoryStreamsProps> = ({ category }) => {
  const [streams, setStreams] = useState<Stream[]>([])
  const [tags, setTags] = useState<Tag[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("startTime")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    fetchStreamsAndTags()
  }, [category.id, currentPage]) // Removed unnecessary dependencies

  const fetchStreamsAndTags = async () => {
    try {
      const [streamsRes, tagsRes] = await Promise.all([
        fetch(
          `/api/streams?categoryId=${category.id}&tags=${selectedTags.join(",")}&sortBy=${sortBy}&sortOrder=${sortOrder}&page=${currentPage}&limit=10`,
        ),
        fetch("/api/tags"),
      ])
      const streamsData = await streamsRes.json()
      const tagsData = await tagsRes.json()
      setStreams(streamsData.streams)
      setTotalPages(streamsData.totalPages)
      setTags(tagsData)
    } catch (error) {
      console.error("Error fetching streams and tags:", error)
    }
  }

  return (
    <div>
      <h2>{category.name} Streams</h2>
      {/* Filters and sorting options (unchanged) */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {streams.map((stream) => (
          <Card key={stream.id}>
            <CardHeader>
              <CardTitle>{stream.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{stream.description}</p>
              <p>Start Time: {new Date(stream.startTime).toLocaleString()}</p>
              <p>Tags: {stream.tags.map((tag) => tag.name).join(", ")}</p>
              <Link href={`/stream/${stream.id}`}>
                <a className="text-blue-500 hover:underline">Watch Stream</a>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
      <div className="mt-4 flex justify-center space-x-2">
        <Button onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))} disabled={currentPage === 1}>
          Previous
        </Button>
        <span>
          Page {currentPage} of {totalPages}
        </span>
        <Button
          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
          disabled={currentPage === totalPages}
        >
          Next
        </Button>
      </div>
    </div>
  )
}
