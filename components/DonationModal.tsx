"use client"

import type React from "react"
import { useState } from "react"
import { useSession } from "next-auth/react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Heart, Gift } from "lucide-react"

interface DonationModalProps {
  creatorId: string
  creatorName: string
  streamId?: string
}

export const DonationModal: React.FC<DonationModalProps> = ({ creatorId, creatorName, streamId }) => {
  const { data: session } = useSession()
  const [amount, setAmount] = useState("")
  const [message, setMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const predefinedAmounts = [50, 100, 250, 500, 1000]

  const handleDonate = async () => {
    if (!session || !amount) return

    setIsLoading(true)
    try {
      const response = await fetch("/api/donations/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          amount: Number.parseFloat(amount),
          message: message.trim() || undefined,
          creatorId,
          streamId,
        }),
      })

      if (response.ok) {
        const data = await response.json()

        // Check if Cashfree is available
        if (typeof window !== "undefined" && (window as any).Cashfree) {
          const cashfree = new (window as any).Cashfree({
            mode: process.env.NODE_ENV === "production" ? "production" : "sandbox",
          })

          cashfree.checkout({
            paymentSessionId: data.paymentSession,
            returnUrl: `${window.location.origin}/donations/success`,
          })
        } else {
          // Fallback: redirect to success page
          window.location.href = `/donations/success?donationId=${data.donationId}`
        }

        setIsOpen(false)
      } else {
        throw new Error("Failed to create donation")
      }
    } catch (error) {
      console.error("Error creating donation:", error)
      alert("Failed to process donation. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  if (!session) {
    return (
      <Button variant="outline" onClick={() => alert("Please sign in to donate")}>
        <Heart className="h-4 w-4 mr-2" />
        Donate
      </Button>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Heart className="h-4 w-4 mr-2" />
          Donate
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Gift className="h-5 w-5 mr-2 text-purple-600" />
            Support {creatorName}
          </DialogTitle>
          <DialogDescription>Show your appreciation with a donation</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {/* Predefined Amounts */}
          <div>
            <Label>Quick amounts (₹)</Label>
            <div className="grid grid-cols-5 gap-2 mt-2">
              {predefinedAmounts.map((preset) => (
                <Button
                  key={preset}
                  variant={amount === preset.toString() ? "default" : "outline"}
                  size="sm"
                  onClick={() => setAmount(preset.toString())}
                >
                  ₹{preset}
                </Button>
              ))}
            </div>
          </div>

          {/* Custom Amount */}
          <div>
            <Label htmlFor="amount">Custom amount (₹)</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="1"
              max="100000"
            />
          </div>

          {/* Message */}
          <div>
            <Label htmlFor="message">Message (optional)</Label>
            <Textarea
              id="message"
              placeholder="Leave a message for the creator..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              maxLength={500}
            />
          </div>

          {/* Donate Button */}
          <Button onClick={handleDonate} disabled={!amount || isLoading} className="w-full">
            {isLoading ? "Processing..." : `Donate ₹${amount || "0"}`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
