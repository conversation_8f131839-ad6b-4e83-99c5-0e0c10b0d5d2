'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface FollowButtonProps {
  targetUserId: string
}

export function FollowButton({ targetUserId }: FollowButtonProps) {
  const { data: session } = useSession()
  const [isFollowing, setIsFollowing] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!session) return
    fetch(`/api/user/${targetUserId}/is-following`)
      .then(res => res.json())
      .then(data => setIsFollowing(data.following))
  }, [targetUserId, session])

  const toggleFollow = async () => {
    setLoading(true)
    await fetch(`/api/follow`, {
      method: isFollowing ? 'DELETE' : 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ targetUserId }),
    })
    setIsFollowing(!isFollowing)
    setLoading(false)
  }

  if (!session || session.user?.id === targetUserId) return null

  return (
    <Button onClick={toggleFollow} disabled={loading} variant={isFollowing ? 'outline' : 'default'}>
      {isFollowing ? 'Unfollow' : 'Follow'}
    </Button>
  )
}
