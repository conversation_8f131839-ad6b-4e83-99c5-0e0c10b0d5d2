"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Users, MapPin, Globe, Calendar, UserPlus, UserMinus } from "lucide-react"

interface UserProfileProps {
  userId: string
}

interface UserData {
  id: string
  name: string
  image?: string
  bio?: string
  website?: string
  location?: string
  followersCount: number
  followingCount: number
  createdAt: string
  streams: Array<{
    id: string
    title: string
    viewerCount: number
    category: { name: string }
  }>
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const { data: session } = useSession()
  const [user, setUser] = useState<UserData | null>(null)
  const [isFollowing, setIsFollowing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [followLoading, setFollowLoading] = useState(false)

  useEffect(() => {
    fetchUserData()
  }, [userId])

  const fetchUserData = async () => {
    try {
      const response = await fetch(`/api/users/${userId}`)
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      }
    } catch (error) {
      console.error("Error fetching user data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFollow = async () => {
    if (!session) return

    setFollowLoading(true)
    try {
      const method = isFollowing ? "DELETE" : "POST"
      const response = await fetch(`/api/users/${userId}/follow`, { method })

      if (response.ok) {
        setIsFollowing(!isFollowing)
        // Update follower count locally
        if (user) {
          setUser({
            ...user,
            followersCount: user.followersCount + (isFollowing ? -1 : 1),
          })
        }
      }
    } catch (error) {
      console.error("Error following/unfollowing user:", error)
    } finally {
      setFollowLoading(false)
    }
  }

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading...</div>
  }

  if (!user) {
    return <div className="text-center p-8">User not found</div>
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={user.image || ""} />
              <AvatarFallback className="text-2xl">{user.name?.charAt(0) || "U"}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold">{user.name}</h1>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                    <span className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {user.followersCount} followers
                    </span>
                    <span>{user.followingCount} following</span>
                  </div>
                </div>
                {session && session.user.id !== userId && (
                  <Button onClick={handleFollow} disabled={followLoading} variant={isFollowing ? "outline" : "default"}>
                    {isFollowing ? (
                      <>
                        <UserMinus className="h-4 w-4 mr-2" />
                        Unfollow
                      </>
                    ) : (
                      <>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Follow
                      </>
                    )}
                  </Button>
                )}
              </div>
              {user.bio && <p className="mt-3 text-gray-700">{user.bio}</p>}
              <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                {user.location && (
                  <span className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {user.location}
                  </span>
                )}
                {user.website && (
                  <a
                    href={user.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center hover:text-purple-600"
                  >
                    <Globe className="h-4 w-4 mr-1" />
                    Website
                  </a>
                )}
                <span className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  Joined {new Date(user.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Live Streams */}
      {user.streams.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Live Now</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {user.streams.map((stream) => (
                <div key={stream.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">{stream.title}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="secondary">{stream.category.name}</Badge>
                      <span className="text-sm text-gray-500">{stream.viewerCount} viewers</span>
                    </div>
                  </div>
                  <Button size="sm">Watch</Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
