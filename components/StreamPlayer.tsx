"use client"

import type React from "react"
import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, Volume2, VolumeX, Maximize, Settings, Wifi, WifiOff } from "lucide-react"
import { io, Socket } from "socket.io-client"

interface StreamPlayerProps {
  streamId: string
  streamUrl?: string
  hlsUrl?: string
  isLive: boolean
  enableWebRTC?: boolean
}

export const StreamPlayer: React.FC<StreamPlayerProps> = ({
  streamId,
  streamUrl,
  hlsUrl,
  isLive,
  enableWebRTC = false
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [streamType, setStreamType] = useState<'hls' | 'webrtc' | 'rtmp'>('hls')
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected')
  const [socket, setSocket] = useState<Socket | null>(null)
  const [peerConnection, setPeerConnection] = useState<RTCPeerConnection | null>(null)

  // Initialize HLS or WebRTC based on availability
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    if (enableWebRTC && isLive) {
      initializeWebRTC()
    } else if (hlsUrl && typeof window !== 'undefined' && 'MediaSource' in window) {
      initializeHLS()
    } else if (streamUrl) {
      // Fallback to direct video source
      video.src = streamUrl
      video.load()
      setStreamType('rtmp')
    }

    return () => {
      cleanup()
    }
  }, [streamId, streamUrl, hlsUrl, isLive, enableWebRTC])

  const initializeHLS = async () => {
    const video = videoRef.current
    if (!video || !hlsUrl) return

    try {
      if (typeof window !== 'undefined') {
        const Hls = (await import('hls.js')).default

        if (Hls.isSupported()) {
          const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: true,
          })

          hls.loadSource(hlsUrl)
          hls.attachMedia(video)

          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            setConnectionStatus('connected')
            setStreamType('hls')
          })

          hls.on(Hls.Events.ERROR, (event, data) => {
            console.error('HLS error:', data)
            setConnectionStatus('disconnected')
          })
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Native HLS support (Safari)
          video.src = hlsUrl
          setStreamType('hls')
          setConnectionStatus('connected')
        }
      }
    } catch (error) {
      console.error('Failed to initialize HLS:', error)
      setConnectionStatus('disconnected')
    }
  }

  const initializeWebRTC = () => {
    if (!enableWebRTC) return

    try {
      setConnectionStatus('connecting')

      // Initialize Socket.IO connection
      const socketConnection = io(process.env.NEXT_PUBLIC_SITE_URL || window.location.origin, {
        path: '/api/socket',
      })

      socketConnection.on('connect', () => {
        console.log('Socket connected for WebRTC')
        socketConnection.emit('join-stream', { streamId })
      })

      socketConnection.on('stream-started', () => {
        setupWebRTCConnection(socketConnection)
      })

      socketConnection.on('offer', async (data: { offer: any, senderId: string }) => {
        if (peerConnection) {
          await handleOffer(data.offer, data.senderId, socketConnection)
        }
      })

      socketConnection.on('ice-candidate', async (data: { candidate: any }) => {
        if (peerConnection && data.candidate) {
          await peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate))
        }
      })

      setSocket(socketConnection)
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error)
      setConnectionStatus('disconnected')
    }
  }

  const setupWebRTCConnection = (socketConnection: Socket) => {
    const pc = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    })

    pc.onicecandidate = (event) => {
      if (event.candidate) {
        socketConnection.emit('ice-candidate', {
          candidate: event.candidate,
          streamId,
        })
      }
    }

    pc.ontrack = (event) => {
      const video = videoRef.current
      if (video && event.streams[0]) {
        video.srcObject = event.streams[0]
        setConnectionStatus('connected')
        setStreamType('webrtc')
      }
    }

    setPeerConnection(pc)
  }

  const handleOffer = async (offer: any, senderId: string, socketConnection: Socket) => {
    if (!peerConnection) return

    try {
      await peerConnection.setRemoteDescription(new RTCSessionDescription(offer))
      const answer = await peerConnection.createAnswer()
      await peerConnection.setLocalDescription(answer)

      socketConnection.emit('answer', {
        answer,
        targetId: senderId,
      })
    } catch (error) {
      console.error('Error handling WebRTC offer:', error)
    }
  }

  const cleanup = () => {
    if (socket) {
      socket.disconnect()
      setSocket(null)
    }

    if (peerConnection) {
      peerConnection.close()
      setPeerConnection(null)
    }

    const video = videoRef.current
    if (video) {
      video.pause()
      video.src = ""
      video.srcObject = null
    }

    setConnectionStatus('disconnected')
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play().catch(console.error)
    }
    setIsPlaying(!isPlaying)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    video.muted = !isMuted
    setIsMuted(!isMuted)
  }

  const toggleFullscreen = () => {
    const video = videoRef.current
    if (!video) return

    if (!isFullscreen) {
      video.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
    setIsFullscreen(!isFullscreen)
  }

  return (
    <Card className="w-full">
      <CardContent className="p-0">
        <div className="relative bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full aspect-video"
            controls={false}
            autoPlay
            muted={isMuted}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />

          {/* Stream Status Overlay */}
          <div className="absolute top-4 left-4 flex gap-2">
            {isLive ? (
              <div className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse" />
                LIVE
              </div>
            ) : (
              <div className="bg-gray-600 text-white px-3 py-1 rounded-full text-sm font-medium">OFFLINE</div>
            )}

            {/* Connection Status */}
            <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'} className="flex items-center gap-1">
              {connectionStatus === 'connected' ? (
                <Wifi className="h-3 w-3" />
              ) : (
                <WifiOff className="h-3 w-3" />
              )}
              {connectionStatus.toUpperCase()}
            </Badge>

            {/* Stream Type */}
            <Badge variant="outline" className="text-white border-white/50">
              {streamType.toUpperCase()}
            </Badge>
          </div>

          {/* Video Controls */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="ghost" onClick={togglePlay} className="text-white hover:bg-white/20">
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <Button size="sm" variant="ghost" onClick={toggleMute} className="text-white hover:bg-white/20">
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </Button>
              </div>
              <Button size="sm" variant="ghost" onClick={toggleFullscreen} className="text-white hover:bg-white/20">
                <Maximize className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Placeholder for offline streams */}
          {!isLive && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50">
              <div className="text-center text-white">
                <Play className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Stream is offline</p>
                <p className="text-sm opacity-75">Check back later</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
