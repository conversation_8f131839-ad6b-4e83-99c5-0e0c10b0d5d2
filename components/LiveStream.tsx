import type React from "react"
import { useState, useEffect } from "react"
import { useRouter } from "next/router"
import type { Tag, Category } from "@prisma/client"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectGroup,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

interface LiveStreamProps {
  // ... existing props
  tags: Tag[]
  categories: Category[]
}

export const LiveStream: React.FC<LiveStreamProps> = ({ tags, categories, ...props }) => {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const router = useRouter()

  // ... existing state and effects

  const handleStartStream = async () => {
    try {
      const response = await fetch("/api/streams", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title,
          description,
          categoryId: selectedCategory,
          tagIds: selectedTags,
        }),
      })

      if (response.ok) {
        const stream = await response.json()
        router.push(`/stream/${stream.id}`)
      } else {
        throw new Error("Failed to start stream")
      }
    } catch (error) {
      console.error("Error starting stream:", error)
    }
  }

  return (
    <div>
      <h1>Start a New Stream</h1>
      <Input type="text" placeholder="Stream Title" value={title} onChange={(e) => setTitle(e.target.value)} />
      <Input
        type="text"
        placeholder="Stream Description"
        value={description}
        onChange={(e) => setDescription(e.target.value)}
      />
      <Select value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)}>
        <option value="">Select a Category</option>
        {categories.map((category) => (
          <option key={category.id} value={category.id}>
            {category.name}
          </option>
        ))}
      </Select>
      <div>
        <h3>Tags</h3>
        {tags.map((tag) => (
          <label key={tag.id}>
            <Checkbox
              checked={selectedTags.includes(tag.id)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedTags([...selectedTags, tag.id])
                } else {
                  setSelectedTags(selectedTags.filter((id) => id !== tag.id))
                }
              }}
            />
            {tag.name}
          </label>
        ))}
      </div>
      <Button onClick={handleStartStream}>Start Stream</Button>
      {/* ... existing stream content */}
    </div>
  )
}
