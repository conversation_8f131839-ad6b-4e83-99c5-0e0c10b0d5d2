"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { useSession } from "next-auth/react"
import { io, type Socket } from "socket.io-client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Send, Users } from "lucide-react"

interface ChatMessage {
  id: string
  content: string
  createdAt: string
  user: {
    id: string
    name: string
    image?: string
  }
}

interface LiveChatProps {
  streamId: string
  viewerCount: number
}

export const LiveChat: React.FC<LiveChatProps> = ({ streamId, viewerCount }) => {
  const { data: session } = useSession()
  const [socket, setSocket] = useState<Socket | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [isConnected, setIsConnected] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Initialize socket connection
    const socketInstance = io(process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000", {
      path: "/api/socket",
    })

    socketInstance.on("connect", () => {
      setIsConnected(true)
      socketInstance.emit("join-stream", streamId)
    })

    socketInstance.on("disconnect", () => {
      setIsConnected(false)
    })

    socketInstance.on("new-message", (message: ChatMessage) => {
      setMessages((prev) => [...prev, message])
    })

    setSocket(socketInstance)

    return () => {
      socketInstance.emit("leave-stream", streamId)
      socketInstance.disconnect()
    }
  }, [streamId])

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const sendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (!socket || !session || !newMessage.trim()) return

    socket.emit("send-message", {
      streamId,
      userId: session.user.id,
      content: newMessage.trim(),
    })

    setNewMessage("")
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-lg">
          <span>Live Chat</span>
          <div className="flex items-center text-sm text-gray-500">
            <Users className="h-4 w-4 mr-1" />
            {viewerCount}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto px-4 space-y-3 max-h-96">
          {messages.map((message) => (
            <div key={message.id} className="flex items-start space-x-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={message.user.image || ""} />
                <AvatarFallback className="text-xs">{message.user.name?.charAt(0) || "U"}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-purple-600">{message.user.name}</span>
                  <span className="text-xs text-gray-500">
                    {new Date(message.createdAt).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                  </span>
                </div>
                <p className="text-sm text-gray-900 break-words">{message.content}</p>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="border-t p-4">
          {session ? (
            <form onSubmit={sendMessage} className="flex space-x-2">
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type a message..."
                className="flex-1"
                maxLength={500}
              />
              <Button type="submit" size="sm" disabled={!isConnected || !newMessage.trim()}>
                <Send className="h-4 w-4" />
              </Button>
            </form>
          ) : (
            <div className="text-center text-sm text-gray-500">
              <p>Sign in to join the chat</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
