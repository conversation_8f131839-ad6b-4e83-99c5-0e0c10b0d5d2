"use client"

import type React from "react"
import { useState } from "react"
import { useSession } from "next-auth/react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Crown, Star, Zap } from "lucide-react"

interface SubscriptionModalProps {
  creatorId: string
  creatorName: string
}

const subscriptionTiers = [
  {
    tier: "BASIC" as const,
    name: "Basic",
    price: 99,
    icon: Star,
    features: ["Access to subscriber-only chat", "Custom emotes", "Priority support"],
  },
  {
    tier: "PREMIUM" as const,
    name: "Premium",
    price: 299,
    icon: Zap,
    features: ["All Basic features", "Exclusive content access", "Monthly Q&A sessions", "Discord access"],
  },
  {
    tier: "VIP" as const,
    name: "VIP",
    price: 599,
    icon: Crown,
    features: ["All Premium features", "1-on-1 monthly call", "Early access to content", "Custom shoutouts"],
  },
]

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({ creatorId, creatorName }) => {
  const { data: session } = useSession()
  const [selectedTier, setSelectedTier] = useState<"BASIC" | "PREMIUM" | "VIP" | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const handleSubscribe = async (tier: "BASIC" | "PREMIUM" | "VIP") => {
    if (!session) return

    setIsLoading(true)
    setSelectedTier(tier)

    try {
      const response = await fetch("/api/subscriptions/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          creatorId,
          tier,
        }),
      })

      if (response.ok) {
        const data = await response.json()

        // Initialize Cashfree Checkout
        const cashfree = new (window as any).Cashfree({
          mode: process.env.NODE_ENV === "production" ? "production" : "sandbox",
        })

        cashfree.checkout({
          paymentSessionId: data.paymentSession,
          returnUrl: `${window.location.origin}/subscriptions/success`,
        })

        setIsOpen(false)
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to create subscription")
      }
    } catch (error) {
      console.error("Error creating subscription:", error)
      alert(error instanceof Error ? error.message : "Failed to process subscription. Please try again.")
    } finally {
      setIsLoading(false)
      setSelectedTier(null)
    }
  }

  if (!session) {
    return (
      <Button onClick={() => alert("Please sign in to subscribe")}>
        <Crown className="h-4 w-4 mr-2" />
        Subscribe
      </Button>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <Crown className="h-4 w-4 mr-2" />
          Subscribe
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Subscribe to {creatorName}</DialogTitle>
          <DialogDescription>
            Choose a subscription tier to support the creator and unlock exclusive benefits
          </DialogDescription>
        </DialogHeader>
        <div className="grid md:grid-cols-3 gap-4">
          {subscriptionTiers.map((subscription) => {
            const Icon = subscription.icon
            const isSelected = selectedTier === subscription.tier
            const isCurrentlyLoading = isLoading && isSelected

            return (
              <Card
                key={subscription.tier}
                className={`relative cursor-pointer transition-all ${
                  subscription.tier === "PREMIUM" ? "border-purple-500 shadow-lg" : "hover:shadow-md"
                }`}
              >
                {subscription.tier === "PREMIUM" && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-purple-500">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <div className="w-12 h-12 mx-auto mb-2 bg-purple-100 rounded-full flex items-center justify-center">
                    <Icon className="h-6 w-6 text-purple-600" />
                  </div>
                  <CardTitle>{subscription.name}</CardTitle>
                  <CardDescription>
                    <span className="text-2xl font-bold text-gray-900">₹{subscription.price}</span>
                    <span className="text-gray-500">/month</span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 mb-6">
                    {subscription.features.map((feature) => (
                      <li key={feature} className="flex items-center text-sm">
                        <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full"
                    variant={subscription.tier === "PREMIUM" ? "default" : "outline"}
                    onClick={() => handleSubscribe(subscription.tier)}
                    disabled={isLoading}
                  >
                    {isCurrentlyLoading ? "Processing..." : `Subscribe for ₹${subscription.price}`}
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </DialogContent>
    </Dialog>
  )
}
