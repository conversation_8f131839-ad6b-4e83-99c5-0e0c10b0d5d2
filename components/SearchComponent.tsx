import type React from "react"
import { useState, useEffect } from "react"
import type { Stream, Tag, Category } from "@prisma/client"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectGroup,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export const SearchComponent: React.FC = () => {
  const [query, setQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState("")
  const [tags, setTags] = useState<Tag[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [results, setResults] = useState<Stream[]>([])

  useEffect(() => {
    const fetchTagsAndCategories = async () => {
      try {
        const [tagsRes, categoriesRes] = await Promise.all([fetch("/api/tags"), fetch("/api/categories")])
        const tagsData = await tagsRes.json()
        const categoriesData = await categoriesRes.json()
        setTags(tagsData)
        setCategories(categoriesData)
      } catch (error) {
        console.error("Error fetching tags and categories:", error)
      }
    }

    fetchTagsAndCategories()
  }, [])

  const handleSearch = async () => {
    try {
      const searchParams = new URLSearchParams({
        query,
        ...(selectedCategory && { category: selectedCategory }),
        ...(selectedTags.length && { tags: selectedTags.join(",") }),
      })
      const response = await fetch(`/api/search?${searchParams.toString()}`)
      if (response.ok) {
        const data = await response.json()
        setResults(data)
      } else {
        throw new Error("Failed to perform search")
      }
    } catch (error) {
      console.error("Error performing search:", error)
    }
  }

  return (
    <div className="space-y-4">
      <Input type="text" placeholder="Search streams..." value={query} onChange={(e) => setQuery(e.target.value)} />
      <div>
        <h3>Tags</h3>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <label key={tag.id} className="flex items-center space-x-2">
              <Checkbox
                checked={selectedTags.includes(tag.id)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedTags([...selectedTags, tag.id])
                  } else {
                    setSelectedTags(selectedTags.filter((id) => id !== tag.id))
                  }
                }}
              />
              <span>{tag.name}</span>
            </label>
          ))}
        </div>
      </div>
      <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value)}>
        <option value="">All Categories</option>
        {categories.map((category) => (
          <option key={category.id} value={category.id}>
            {category.name}
          </option>
        ))}
      </Select>
      <Button onClick={handleSearch}>Search</Button>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {results.map((stream) => (
          <Card key={stream.id}>
            <CardHeader>
              <CardTitle>{stream.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{stream.description}</p>
              <p>Category: {stream.category.name}</p>
              <p>Tags: {stream.tags.map((tag) => tag.name).join(", ")}</p>
              <Link href={`/stream/${stream.id}`}>
                <a className="text-blue-500 hover:underline">Watch Stream</a>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
