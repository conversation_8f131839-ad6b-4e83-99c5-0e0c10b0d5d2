import { describe, it, expect, beforeEach, afterEach, jest } from "@jest/globals"
import { createMocks } from "node-mocks-http"
import { getServerSession } from "next-auth"
import handler from "@/app/api/donations/create/route"
import prisma from "@/lib/prisma"

// Mock dependencies
jest.mock("next-auth")
jest.mock("@/lib/prisma", () => ({
  user: {
    findUnique: jest.fn(),
  },
  donation: {
    create: jest.fn(),
    update: jest.fn(),
  },
}))

jest.mock("@/lib/cashfree", () => ({
  Cashfree: {
    PGCreateOrder: jest.fn(),
  },
}))

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe("/api/donations/create", () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  describe("POST", () => {
    it("should create a donation successfully", async () => {
      // Mock authenticated user
      mockGetServerSession.mockResolvedValue({
        user: { email: "<EMAIL>" },
      } as any)

      // Mock user lookup
      mockPrisma.user.findUnique.mockResolvedValueOnce({
        id: "user-1",
        email: "<EMAIL>",
        name: "Test User",
      } as any)

      // Mock creator lookup
      mockPrisma.user.findUnique.mockResolvedValueOnce({
        id: "creator-1",
        email: "<EMAIL>",
        name: "Creator User",
      } as any)

      // Mock donation creation
      mockPrisma.donation.create.mockResolvedValue({
        id: "donation-1",
        amount: 100,
        message: "Great stream!",
        userId: "user-1",
        creatorId: "creator-1",
        status: "PENDING",
      } as any)

      // Mock Cashfree order creation
      const { Cashfree } = require("@/lib/cashfree")
      Cashfree.PGCreateOrder.mockResolvedValue({
        data: {
          payment_session_id: "session-123",
          order_id: "order-123",
        },
      })

      const { req, res } = createMocks({
        method: "POST",
        body: {
          amount: 100,
          message: "Great stream!",
          creatorId: "creator-1",
        },
      })

      const response = await handler(req as any)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty("donationId")
      expect(data).toHaveProperty("paymentSession")
      expect(data).toHaveProperty("orderId")
      expect(mockPrisma.donation.create).toHaveBeenCalledWith({
        data: {
          amount: 100,
          message: "Great stream!",
          userId: "user-1",
          creatorId: "creator-1",
          streamId: undefined,
          status: "PENDING",
        },
      })
    })

    it("should return 401 if user is not authenticated", async () => {
      mockGetServerSession.mockResolvedValue(null)

      const { req } = createMocks({
        method: "POST",
        body: {
          amount: 100,
          creatorId: "creator-1",
        },
      })

      const response = await handler(req as any)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe("Unauthorized")
    })

    it("should return 400 for invalid input", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { email: "<EMAIL>" },
      } as any)

      const { req } = createMocks({
        method: "POST",
        body: {
          amount: -10, // Invalid amount
          creatorId: "creator-1",
        },
      })

      const response = await handler(req as any)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe("Invalid input data")
    })

    it("should return 404 if creator not found", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { email: "<EMAIL>" },
      } as any)

      // Mock user lookup
      mockPrisma.user.findUnique.mockResolvedValueOnce({
        id: "user-1",
        email: "<EMAIL>",
        name: "Test User",
      } as any)

      // Mock creator not found
      mockPrisma.user.findUnique.mockResolvedValueOnce(null)

      const { req } = createMocks({
        method: "POST",
        body: {
          amount: 100,
          creatorId: "nonexistent-creator",
        },
      })

      const response = await handler(req as any)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe("Creator not found")
    })

    it("should handle payment gateway errors", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { email: "<EMAIL>" },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValueOnce({
        id: "user-1",
        email: "<EMAIL>",
        name: "Test User",
      } as any)

      mockPrisma.user.findUnique.mockResolvedValueOnce({
        id: "creator-1",
        email: "<EMAIL>",
        name: "Creator User",
      } as any)

      mockPrisma.donation.create.mockResolvedValue({
        id: "donation-1",
        amount: 100,
        userId: "user-1",
        creatorId: "creator-1",
        status: "PENDING",
      } as any)

      // Mock Cashfree error
      const { Cashfree } = require("@/lib/cashfree")
      Cashfree.PGCreateOrder.mockResolvedValue({
        data: null,
        error: "Payment gateway error",
      })

      const { req } = createMocks({
        method: "POST",
        body: {
          amount: 100,
          creatorId: "creator-1",
        },
      })

      const response = await handler(req as any)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe("Internal server error")
    })
  })
})
