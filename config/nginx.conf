worker_processes auto;
rtmp_auto_push on;
events {
    worker_connections 1024;
}

# RTMP configuration
rtmp {
    server {
        listen 1935;
        chunk_size 4000;
        
        # Live streaming application
        application live {
            live on;
            
            # Enable HLS
            hls on;
            hls_path /var/www/html/live;
            hls_fragment 3;
            hls_playlist_length 60;
            hls_continuous on;
            hls_cleanup on;
            hls_nested on;
            
            # Enable DASH
            dash on;
            dash_path /var/www/html/dash;
            dash_fragment 3;
            dash_playlist_length 60;
            dash_nested on;
            dash_cleanup on;
            
            # Authentication
            on_publish http://app:3000/api/streaming/auth;
            on_publish_done http://app:3000/api/streaming/publish_done;
            
            # Recording
            record all;
            record_path /var/www/html/recordings;
            record_unique on;
            record_suffix .flv;
            
            # Transcoding for multiple bitrates
            exec ffmpeg -i rtmp://localhost/live/$name
                -c:a aac -b:a 128k -c:v libx264 -b:v 2500k -f flv -g 30 -r 30 -s 1920x1080 -preset superfast -profile:v baseline rtmp://localhost/hls/$name_1080p2628kbs
                -c:a aac -b:a 128k -c:v libx264 -b:v 1000k -f flv -g 30 -r 30 -s 1280x720 -preset superfast -profile:v baseline rtmp://localhost/hls/$name_720p1128kbs
                -c:a aac -b:a 128k -c:v libx264 -b:v 500k -f flv -g 30 -r 30 -s 854x480 -preset superfast -profile:v baseline rtmp://localhost/hls/$name_480p628kbs;
        }
        
        # HLS application for transcoded streams
        application hls {
            live on;
            hls on;
            hls_path /var/www/html/live;
            hls_fragment 3;
            hls_playlist_length 60;
            hls_continuous on;
            hls_cleanup on;
            hls_nested on;
            
            # Variant playlist
            hls_variant _1080p2628kbs BANDWIDTH=2628000,RESOLUTION=1920x1080;
            hls_variant _720p1128kbs BANDWIDTH=1128000,RESOLUTION=1280x720;
            hls_variant _480p628kbs BANDWIDTH=628000,RESOLUTION=854x480;
        }
    }
}

# HTTP configuration
http {
    sendfile off;
    tcp_nopush on;
    directio 512;
    default_type application/octet-stream;
    
    server {
        listen 80;
        
        # CORS headers for HLS
        location ~* \.(m3u8|ts)$ {
            add_header Cache-Control no-cache;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
            add_header Access-Control-Allow-Headers Range;
            
            # Serve HLS files
            root /var/www/html;
        }
        
        # Serve DASH files
        location ~* \.(mpd|m4s)$ {
            add_header Cache-Control no-cache;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
            add_header Access-Control-Allow-Headers Range;
            
            root /var/www/html;
        }
        
        # Serve recordings
        location /recordings {
            add_header Access-Control-Allow-Origin *;
            root /var/www/html;
        }
        
        # RTMP statistics
        location /stat {
            rtmp_stat all;
            rtmp_stat_stylesheet stat.xsl;
            add_header Access-Control-Allow-Origin *;
        }
        
        location /stat.xsl {
            root /var/www/html;
        }
        
        # Control interface
        location /control {
            rtmp_control all;
            add_header Access-Control-Allow-Origin *;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
