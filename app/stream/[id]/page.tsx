"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Navigation } from "@/components/Navigation"
import { StreamPlayer } from "@/components/StreamPlayer"
import { LiveChat } from "@/components/LiveChat"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Eye } from "lucide-react"
import { FollowButton } from "@/components/FollowButton" // ✅ Make sure this file exists

interface StreamData {
  id: string
  title: string
  description?: string
  isLive: boolean
  viewerCount: number
  streamUrl?: string
  creator: {
    id: string
    name: string
    image?: string
  }
  category?: {
    name: string
  }
  tags: Array<{ name: string }>
}

export default function StreamPage() {
  const params = useParams()
  const streamId = params.id as string
  const [stream, setStream] = useState<StreamData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (streamId) fetchStreamData()
  }, [streamId])

  const fetchStreamData = async () => {
    try {
      const response = await fetch(`/api/streams/${streamId}`)
      if (response.ok) {
        const data = await response.json()
        setStream(data)
      }
    } catch (error) {
      console.error("Error fetching stream data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="aspect-video bg-gray-200 rounded-lg mb-6" />
            <div className="h-8 bg-gray-200 rounded mb-4" />
            <div className="h-4 bg-gray-200 rounded w-2/3" />
          </div>
        </div>
      </div>
    )
  }

  if (!stream) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Stream not found</h1>
            <p className="text-gray-600">The stream you're looking for doesn't exist or has been removed.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Stream Player */}
            <StreamPlayer streamId={streamId} streamUrl={stream.streamUrl} isLive={stream.isLive} />

            {/* Stream Info */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">{stream.title}</h1>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        {stream.viewerCount} viewers
                      </div>
                      <Badge variant={stream.isLive ? "default" : "secondary"}>
                        {stream.isLive ? "LIVE" : "OFFLINE"}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Creator Info */}
                <div className="flex items-center space-x-3 mb-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={stream.creator.image || ""} />
                    <AvatarFallback>{stream.creator.name?.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium text-gray-900">{stream.creator.name}</h3>
                    <p className="text-sm text-gray-500">Creator</p>
                    <FollowButton targetUserId={stream.creator.id} /> {/* ✅ Follow Button */}
                  </div>
                </div>

                {/* Description */}
                {stream.description && (
                  <div className="mb-4">
                    <p className="text-gray-700">{stream.description}</p>
                  </div>
                )}

                {/* Tags and Category */}
                <div className="flex items-center flex-wrap gap-2">
                  {stream.category?.name && (
                    <Badge variant="outline">{stream.category.name}</Badge>
                  )}
                  {stream.tags?.map((tag) => (
                    <Badge key={tag.name} variant="secondary">
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Live Chat */}
            <LiveChat streamId={streamId} viewerCount={stream.viewerCount} />
          </div>
        </div>
      </div>
    </div>
  )
}
