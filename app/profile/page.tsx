"use client"

import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ProfilePage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return <div>Loading...</div>
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card>
          <CardHeader>
            <CardTitle>Sign In Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please sign in to view your profile.</p>
            <Button onClick={() => (window.location.href = "/api/auth/signin")}>Sign In</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <strong>Name:</strong> {session.user?.name}
            </div>
            <div>
              <strong>Email:</strong> {session.user?.email}
            </div>
            <div>
              <strong>Role:</strong> {session.user?.role || "user"}
            </div>
            <Button onClick={() => (window.location.href = "/api/auth/signout")}>Sign Out</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
