"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Play,
  Users,
  BarChart3,
  Settings
} from "lucide-react"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [summary, setSummary] = useState({
    totalStreams: 0,
    totalFollowers: 0,
    totalViews: 0,
    totalRevenue: 0,
    recentStreams: [],
    topDonations: [],
    trendingFollowers: []
  })

  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/auth/signin")
  }, [session, status, router])

  useEffect(() => {
    const fetchSummary = async () => {
      try {
        const res = await fetch("/api/dashboard/summary")
        if (res.ok) {
          const data = await res.json()
          setSummary(data)
        }
      } catch (err) {
        console.error("Failed to fetch dashboard summary", err)
      }
    }

    if (session) {
      fetchSummary()
    }
  }, [session])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        Loading...
      </div>
    )
  }

  if (!session) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {session.user?.name}!
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your streams
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Streams</CardTitle>
              <Play className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalStreams}</div>
              <p className="text-xs text-muted-foreground">
                You’ve streamed {summary.totalStreams} times
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Followers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalFollowers}</div>
              <p className="text-xs text-muted-foreground">
                Connected fans following you
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Views</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalViews}</div>
              <p className="text-xs text-muted-foreground">
                Total views across all streams
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{summary.totalRevenue.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                Donations received
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Get started with your streaming journey
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
            <Button
              className="w-full"
             onClick={() => router.push("/dashboard/stream/create")}
                 >
            <Play className="mr-2 h-4 w-4" />
             Start New Stream
           </Button>

  <Button
    variant="outline"
    className="w-full"
    onClick={() => router.push("/dashboard/stream/settings")}
  >
    <Settings className="mr-2 h-4 w-4" />
    Stream Settings
  </Button>
            </CardContent>
          </Card>

          {/* Recent Streams */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Streams</CardTitle>
              <CardDescription>Your latest live sessions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {summary.recentStreams.length > 0 ? (
                summary.recentStreams.map((stream: any) => (
                  <div key={stream.id} className="flex justify-between text-sm">
                    <span>{stream.title}</span>
                    <span className="text-muted-foreground">
                      {new Date(stream.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No recent streams.</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Donations + Followers */}
        <div className="grid md:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Donations</CardTitle>
              <CardDescription>Highest supporters</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {summary.topDonations.length > 0 ? (
                summary.topDonations.map((donation: any) => (
                  <div key={donation.id} className="flex justify-between text-sm">
                    <span>
                      {donation.user?.name || "Anonymous"} - ₹{donation.amount}
                    </span>
                    <span className="text-muted-foreground">
                      {new Date(donation.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No donations yet.</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Trending Followers</CardTitle>
              <CardDescription>Newest connections</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {summary.trendingFollowers.length > 0 ? (
                summary.trendingFollowers.map((follow: any, index: number) => (
                  <div key={index} className="text-sm flex items-center space-x-2">
                    <img
                      src={follow.follower.image || ""}
                      alt="avatar"
                      className="w-6 h-6 rounded-full"
                    />
                    <span>{follow.follower.name || "Unknown"}</span>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No followers yet.</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
