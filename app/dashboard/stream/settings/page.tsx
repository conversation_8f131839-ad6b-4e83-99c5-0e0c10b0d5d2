'use client'

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"

export default function StreamSettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [isPrivate, setIsPrivate] = useState(false)
  const [isMonetized, setIsMonetized] = useState(false)
  const [enableModeration, setEnableModeration] = useState(true)

  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/auth/signin")
  }, [session, status, router])

  if (status === "loading" || !session) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>
  }

  const handleSave = async () => {
    // TODO: Call API to save stream settings
    console.log({
      title,
      description,
      isPrivate,
      isMonetized,
      enableModeration
    })
    alert("✅ Settings saved (functionality pending backend integration)")
  }

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Stream Settings</CardTitle>
            <CardDescription>Configure your stream preferences, monetization, and privacy.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title">Stream Title</Label>
              <Input id="title" placeholder="Enter stream title..." value={title} onChange={(e) => setTitle(e.target.value)} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" placeholder="What's your stream about?" value={description} onChange={(e) => setDescription(e.target.value)} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="private">Private Stream</Label>
              <Switch id="private" checked={isPrivate} onCheckedChange={setIsPrivate} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="monetized">Enable Monetization</Label>
              <Switch id="monetized" checked={isMonetized} onCheckedChange={setIsMonetized} />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="moderation">Enable Chat Moderation</Label>
              <Switch id="moderation" checked={enableModeration} onCheckedChange={setEnableModeration} />
            </div>

            <Button className="w-full mt-4" onClick={handleSave}>
              Save Settings
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
