import { NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const period = searchParams.get("period") || "30" // days
    const periodDays = parseInt(period)

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - periodDays)

    // Get analytics data
    const [
      totalRevenue,
      totalDonations,
      totalSubscriptions,
      totalViews,
      totalStreams,
      recentDonations,
      subscriptionsByTier,
      viewsByDay,
      topStreams,
    ] = await Promise.all([
      // Total revenue
      prisma.donation.aggregate({
        where: {
          creatorId: user.id,
          status: "SUCCESS",
          createdAt: { gte: startDate },
        },
        _sum: { amount: true },
      }),
      
      // Total donations count
      prisma.donation.count({
        where: {
          creatorId: user.id,
          status: "SUCCESS",
          createdAt: { gte: startDate },
        },
      }),
      
      // Total active subscriptions
      prisma.subscription.count({
        where: {
          creatorId: user.id,
          status: "ACTIVE",
        },
      }),
      
      // Total views
      prisma.stream.aggregate({
        where: {
          creatorId: user.id,
          createdAt: { gte: startDate },
        },
        _sum: { totalViews: true },
      }),
      
      // Total streams
      prisma.stream.count({
        where: {
          creatorId: user.id,
          createdAt: { gte: startDate },
        },
      }),
      
      // Recent donations
      prisma.donation.findMany({
        where: {
          creatorId: user.id,
          status: "SUCCESS",
          createdAt: { gte: startDate },
        },
        include: {
          user: {
            select: { name: true, image: true },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 10,
      }),
      
      // Subscriptions by tier
      prisma.subscription.groupBy({
        by: ["tier"],
        where: {
          creatorId: user.id,
          status: "ACTIVE",
        },
        _count: { tier: true },
        _sum: { amount: true },
      }),
      
      // Views by day (last 30 days)
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          SUM(total_views) as views
        FROM "Stream"
        WHERE creator_id = ${user.id}
          AND created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `,
      
      // Top performing streams
      prisma.stream.findMany({
        where: {
          creatorId: user.id,
          createdAt: { gte: startDate },
        },
        orderBy: { totalViews: "desc" },
        take: 5,
        select: {
          id: true,
          title: true,
          totalViews: true,
          viewerCount: true,
          createdAt: true,
        },
      }),
    ])

    const analytics = {
      overview: {
        totalRevenue: totalRevenue._sum.amount || 0,
        totalDonations,
        totalSubscriptions,
        totalViews: totalViews._sum.totalViews || 0,
        totalStreams,
        averageViewsPerStream: totalStreams > 0 ? Math.round((totalViews._sum.totalViews || 0) / totalStreams) : 0,
      },
      recentDonations,
      subscriptionsByTier,
      viewsByDay,
      topStreams,
      period: periodDays,
    }

    return NextResponse.json(analytics)
  } catch (error) {
    console.error("Analytics error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
