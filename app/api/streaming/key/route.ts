import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import crypto from "crypto"

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get user's current stream or create one
    let stream = await prisma.stream.findFirst({
      where: {
        creatorId: user.id,
        isLive: false, // Get the most recent non-live stream
      },
      orderBy: { createdAt: "desc" },
    })

    if (!stream) {
      // Create a default stream for the user
      stream = await prisma.stream.create({
        data: {
          title: `${user.name}'s Stream`,
          description: "Live streaming session",
          creatorId: user.id,
          streamKey: crypto.randomBytes(16).toString("hex"),
        },
      })
    }

    const streamingConfig = {
      streamKey: stream.streamKey,
      rtmpUrl: `rtmp://${process.env.RTMP_SERVER_URL || "localhost:1935"}/live`,
      fullRtmpUrl: `rtmp://${process.env.RTMP_SERVER_URL || "localhost:1935"}/live/${stream.streamKey}`,
      hlsUrl: `http://${process.env.HLS_SERVER_URL || "localhost:8000"}/live/${stream.streamKey}/index.m3u8`,
      webrtcUrl: `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/webrtc/connect`,
      streamId: stream.id,
      instructions: {
        obs: {
          server: `rtmp://${process.env.RTMP_SERVER_URL || "localhost:1935"}/live`,
          key: stream.streamKey,
        },
        ffmpeg: `ffmpeg -i input.mp4 -c:v libx264 -c:a aac -f flv rtmp://${process.env.RTMP_SERVER_URL || "localhost:1935"}/live/${stream.streamKey}`,
      },
    }

    return NextResponse.json(streamingConfig)
  } catch (error) {
    console.error("Error getting stream key:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Generate new stream key
    const newStreamKey = crypto.randomBytes(16).toString("hex")

    // Update all user's streams with new key (or create new stream)
    const existingStream = await prisma.stream.findFirst({
      where: {
        creatorId: user.id,
        isLive: false,
      },
      orderBy: { createdAt: "desc" },
    })

    let stream
    if (existingStream) {
      stream = await prisma.stream.update({
        where: { id: existingStream.id },
        data: { streamKey: newStreamKey },
      })
    } else {
      stream = await prisma.stream.create({
        data: {
          title: `${user.name}'s Stream`,
          description: "Live streaming session",
          creatorId: user.id,
          streamKey: newStreamKey,
        },
      })
    }

    const streamingConfig = {
      streamKey: stream.streamKey,
      rtmpUrl: `rtmp://${process.env.RTMP_SERVER_URL || "localhost:1935"}/live`,
      fullRtmpUrl: `rtmp://${process.env.RTMP_SERVER_URL || "localhost:1935"}/live/${stream.streamKey}`,
      hlsUrl: `http://${process.env.HLS_SERVER_URL || "localhost:8000"}/live/${stream.streamKey}/index.m3u8`,
      streamId: stream.id,
      message: "New stream key generated successfully",
    }

    return NextResponse.json(streamingConfig)
  } catch (error) {
    console.error("Error generating stream key:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
