import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { watermarkService } from "@/lib/security/watermark"
import { moderationService } from "@/lib/security/moderation"
import { validateFileUpload } from "@/lib/middleware/validation"
import { uploadRateLimit } from "@/lib/middleware/rateLimit"
import prisma from "@/lib/prisma"
import crypto from "crypto"

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await uploadRateLimit(req)
    if (rateLimitResult) return rateLimitResult

    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const formData = await req.formData()
    const file = formData.get("file") as File
    const type = formData.get("type") as string // "avatar", "thumbnail", "content"
    const streamId = formData.get("streamId") as string

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    // Validate file
    const validation = validateFileUpload(file, {
      maxSize: type === "avatar" ? 5 * 1024 * 1024 : 50 * 1024 * 1024, // 5MB for avatars, 50MB for content
      allowedTypes: type === "avatar" 
        ? ["image/jpeg", "image/png", "image/webp"]
        : ["image/jpeg", "image/png", "image/webp", "video/mp4", "video/webm"],
      allowedExtensions: type === "avatar"
        ? [".jpg", ".jpeg", ".png", ".webp"]
        : [".jpg", ".jpeg", ".png", ".webp", ".mp4", ".webm"],
    })

    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: 400 })
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Content moderation
    if (file.type.startsWith("image/")) {
      const moderationResult = await moderationService.moderateImage(buffer)
      if (!moderationResult.isAllowed) {
        return NextResponse.json(
          { error: "Content violates community guidelines", reason: moderationResult.reason },
          { status: 400 }
        )
      }
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()
    const uniqueId = crypto.randomBytes(16).toString("hex")
    const filename = `${type}_${user.id}_${uniqueId}.${fileExtension}`

    // Create upload directory
    const uploadDir = join(process.cwd(), "uploads", type)
    await mkdir(uploadDir, { recursive: true })

    let processedBuffer = buffer
    let finalPath = join(uploadDir, filename)

    // Apply watermark for content
    if (file.type.startsWith("image/") && type === "content") {
      const watermarkText = watermarkService.generateUniqueWatermark(user.id, uniqueId)
      processedBuffer = await watermarkService.addImageWatermark(buffer, {
        text: watermarkText,
        position: "bottom-right",
        opacity: 0.3,
      })

      // Also add invisible watermark
      processedBuffer = await watermarkService.addInvisibleWatermark(
        processedBuffer,
        JSON.stringify({
          userId: user.id,
          contentId: uniqueId,
          timestamp: Date.now(),
          platform: "FansXcluziv",
        })
      )
    }

    // Apply watermark for videos (if needed)
    if (file.type.startsWith("video/") && type === "content") {
      // Save original file first
      await writeFile(finalPath, buffer)
      
      // Create watermarked version
      const watermarkedPath = join(uploadDir, `watermarked_${filename}`)
      const watermarkText = watermarkService.generateUniqueWatermark(user.id, uniqueId)
      
      await watermarkService.addVideoWatermark(finalPath, watermarkedPath, {
        text: watermarkText,
        position: "bottom-right",
        opacity: 0.3,
      })
      
      finalPath = watermarkedPath
    } else {
      // Save processed file
      await writeFile(finalPath, processedBuffer)
    }

    // Create media record in database
    const mediaRecord = await prisma.media.create({
      data: {
        filename,
        originalName: file.name,
        mimeType: file.type,
        size: file.size,
        type,
        userId: user.id,
        streamId: streamId || undefined,
        path: finalPath,
        url: `/uploads/${type}/${filename}`,
      },
    })

    // Update user avatar if type is avatar
    if (type === "avatar") {
      await prisma.user.update({
        where: { id: user.id },
        data: { image: mediaRecord.url },
      })
    }

    // Update stream thumbnail if type is thumbnail
    if (type === "thumbnail" && streamId) {
      await prisma.stream.update({
        where: { id: streamId },
        data: { thumbnailUrl: mediaRecord.url },
      })
    }

    return NextResponse.json({
      id: mediaRecord.id,
      filename: mediaRecord.filename,
      url: mediaRecord.url,
      type: mediaRecord.type,
      size: mediaRecord.size,
      mimeType: mediaRecord.mimeType,
      message: "File uploaded successfully",
    })

  } catch (error) {
    console.error("Upload error:", error)
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const type = searchParams.get("type")
    const streamId = searchParams.get("streamId")

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const whereClause: any = { userId: user.id }
    if (type) whereClause.type = type
    if (streamId) whereClause.streamId = streamId

    const mediaFiles = await prisma.media.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: 50,
      select: {
        id: true,
        filename: true,
        originalName: true,
        mimeType: true,
        size: true,
        type: true,
        url: true,
        createdAt: true,
      },
    })

    return NextResponse.json(mediaFiles)
  } catch (error) {
    console.error("Error fetching media files:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const mediaId = searchParams.get("id")

    if (!mediaId) {
      return NextResponse.json({ error: "Media ID required" }, { status: 400 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Verify ownership
    const mediaFile = await prisma.media.findFirst({
      where: {
        id: mediaId,
        userId: user.id,
      },
    })

    if (!mediaFile) {
      return NextResponse.json({ error: "Media file not found" }, { status: 404 })
    }

    // Delete file from filesystem
    try {
      const fs = require("fs").promises
      await fs.unlink(mediaFile.path)
    } catch (error) {
      console.error("Error deleting file from filesystem:", error)
    }

    // Delete from database
    await prisma.media.delete({
      where: { id: mediaId },
    })

    return NextResponse.json({ message: "Media file deleted successfully" })
  } catch (error) {
    console.error("Error deleting media file:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
