import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { Cashfree } from "@/lib/cashfree"
import { z } from "zod"

const payoutSchema = z.object({
  amount: z.number().min(100).max(100000), // Minimum ₹100, Maximum ₹1,00,000
  bankAccount: z.string().min(8).max(20),
  ifscCode: z.string().length(11),
})

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = payoutSchema.parse(body)

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user has sufficient balance
    if (user.availableBalance < validatedData.amount) {
      return NextResponse.json(
        { error: "Insufficient balance" },
        { status: 400 }
      )
    }

    // Check for pending payouts
    const pendingPayout = await prisma.payout.findFirst({
      where: {
        userId: user.id,
        status: { in: ["PENDING", "PROCESSING"] },
      },
    })

    if (pendingPayout) {
      return NextResponse.json(
        { error: "You have a pending payout request" },
        { status: 400 }
      )
    }

    // Create payout record
    const payout = await prisma.payout.create({
      data: {
        userId: user.id,
        amount: validatedData.amount,
        bankAccount: validatedData.bankAccount,
        ifscCode: validatedData.ifscCode,
        status: "PENDING",
      },
    })

    // Deduct amount from available balance
    await prisma.user.update({
      where: { id: user.id },
      data: {
        availableBalance: { decrement: validatedData.amount },
      },
    })

    // Create Cashfree payout (if using Cashfree for payouts)
    try {
      const payoutRequest = {
        payout_id: `payout_${payout.id}_${Date.now()}`,
        amount: validatedData.amount,
        beneficiary: {
          name: user.name || "Creator",
          email: user.email!,
          phone: "**********", // You should collect this
          bank_account: validatedData.bankAccount,
          ifsc: validatedData.ifscCode,
        },
      }

      // Note: Implement actual Cashfree payout API call here
      // const payoutResponse = await Cashfree.createPayout(payoutRequest)
      
      // For now, we'll mark it as processing
      await prisma.payout.update({
        where: { id: payout.id },
        data: { status: "PROCESSING" },
      })

    } catch (payoutError) {
      console.error("Payout creation failed:", payoutError)
      
      // Refund the amount back to user's balance
      await prisma.user.update({
        where: { id: user.id },
        data: {
          availableBalance: { increment: validatedData.amount },
        },
      })

      await prisma.payout.update({
        where: { id: payout.id },
        data: { status: "FAILED" },
      })

      return NextResponse.json(
        { error: "Failed to process payout" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      payoutId: payout.id,
      amount: validatedData.amount,
      status: "PROCESSING",
      message: "Payout request submitted successfully",
    })
  } catch (error) {
    console.error("Payout request error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const payouts = await prisma.payout.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: "desc" },
      take: 20,
    })

    return NextResponse.json(payouts)
  } catch (error) {
    console.error("Error fetching payouts:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
