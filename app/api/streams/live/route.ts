import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET() {
  try {
    const liveStreams = await prisma.stream.findMany({
      where: { isLive: true },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
            followersCount: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        tags: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { viewerCount: "desc" },
      take: 50,
    })

    return NextResponse.json(liveStreams)
  } catch (error) {
    console.error("Error fetching live streams:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { streamId, action } = await req.json()

    if (!streamId || !action) {
      return NextResponse.json(
        { error: "Stream ID and action are required" },
        { status: 400 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Verify user owns the stream
    const stream = await prisma.stream.findFirst({
      where: {
        id: streamId,
        creatorId: user.id,
      },
    })

    if (!stream) {
      return NextResponse.json(
        { error: "Stream not found or unauthorized" },
        { status: 404 }
      )
    }

    let updateData: any = {}

    switch (action) {
      case "start":
        updateData = {
          isLive: true,
          startTime: new Date(),
        }
        break
      case "stop":
        updateData = {
          isLive: false,
          endTime: new Date(),
        }
        break
      case "update_viewers":
        const { viewerCount } = await req.json()
        updateData = {
          viewerCount: viewerCount || 0,
          totalViews: { increment: 1 },
        }
        break
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        )
    }

    const updatedStream = await prisma.stream.update({
      where: { id: streamId },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        category: true,
        tags: true,
      },
    })

    return NextResponse.json(updatedStream)
  } catch (error) {
    console.error("Error managing live stream:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
