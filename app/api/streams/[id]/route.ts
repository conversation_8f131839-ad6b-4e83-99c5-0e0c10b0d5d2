import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const streamId = params.id;

    const stream = await prisma.stream.findUnique({
      where: { id: streamId },
      include: {
        creator: {
          select: { id: true, name: true, image: true },
        },
        category: true,
        tags: {
          select: { name: true },
        },
      },
    });

    if (!stream) {
      return NextResponse.json({ error: "Stream not found" }, { status: 404 });
    }

    return NextResponse.json(stream, { status: 200 });
  } catch (error) {
    console.error("[STREAM_FETCH_ERROR]", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
