import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { Cashfree } from "@/lib/cashfree"
import { z } from "zod"

const donationSchema = z.object({
  amount: z.number().min(1).max(100000),
  message: z.string().optional(),
  creatorId: z.string(),
  streamId: z.string().optional(),
})

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = donationSchema.parse(body)

    // Get current user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Verify creator exists
    const creator = await prisma.user.findUnique({
      where: { id: validatedData.creatorId },
    })

    if (!creator) {
      return NextResponse.json({ error: "Creator not found" }, { status: 404 })
    }

    // Create donation record
    const donation = await prisma.donation.create({
      data: {
        amount: validatedData.amount,
        message: validatedData.message,
        userId: user.id,
        creatorId: validatedData.creatorId,
        streamId: validatedData.streamId,
        status: "PENDING",
      },
    })

    // Create Cashfree order
    const orderId = `donation_${donation.id}_${Date.now()}`
    
    const orderRequest = {
      order_amount: validatedData.amount,
      order_currency: "INR",
      order_id: orderId,
      customer_details: {
        customer_id: user.id,
        customer_name: user.name || "Anonymous",
        customer_email: user.email!,
        customer_phone: "9999999999", // You should collect this in user profile
      },
      order_meta: {
        return_url: `${process.env.NEXTAUTH_URL}/donations/success?donationId=${donation.id}`,
        notify_url: `${process.env.NEXTAUTH_URL}/api/payments/webhook`,
      },
    }

    const response = await Cashfree.PGCreateOrder("2023-08-01", orderRequest)
    
    if (response.data) {
      return NextResponse.json({
        donationId: donation.id,
        paymentSession: response.data.payment_session_id,
        orderId: response.data.order_id,
      })
    } else {
      throw new Error("Failed to create payment order")
    }
  } catch (error) {
    console.error("Donation creation error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
