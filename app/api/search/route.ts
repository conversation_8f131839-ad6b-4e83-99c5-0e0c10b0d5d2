import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const query = searchParams.get("q")
    const type = searchParams.get("type") // "users", "streams", "all"
    const category = searchParams.get("category")
    const tags = searchParams.get("tags")?.split(",")
    const limit = parseInt(searchParams.get("limit") || "20")
    const offset = parseInt(searchParams.get("offset") || "0")

    if (!query || query.length < 2) {
      return NextResponse.json({ error: "Search query must be at least 2 characters" }, { status: 400 })
    }

    const results: any = {
      users: [],
      streams: [],
      categories: [],
      tags: [],
    }

    // Search users
    if (!type || type === "users" || type === "all") {
      results.users = await prisma.user.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: "insensitive" } },
            { bio: { contains: query, mode: "insensitive" } },
          ],
          role: { in: ["creator", "user"] }, // Exclude admin accounts from search
        },
        select: {
          id: true,
          name: true,
          image: true,
          bio: true,
          followersCount: true,
          role: true,
          _count: {
            select: {
              streams: true,
            },
          },
        },
        orderBy: [
          { followersCount: "desc" },
          { name: "asc" },
        ],
        take: type === "users" ? limit : 10,
        skip: type === "users" ? offset : 0,
      })
    }

    // Search streams
    if (!type || type === "streams" || type === "all") {
      const streamWhereClause: any = {
        OR: [
          { title: { contains: query, mode: "insensitive" } },
          { description: { contains: query, mode: "insensitive" } },
        ],
      }

      if (category) {
        streamWhereClause.category = {
          name: { equals: category, mode: "insensitive" },
        }
      }

      if (tags && tags.length > 0) {
        streamWhereClause.tags = {
          some: {
            name: { in: tags },
          },
        }
      }

      results.streams = await prisma.stream.findMany({
        where: streamWhereClause,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              image: true,
              followersCount: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          tags: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [
          { isLive: "desc" },
          { viewerCount: "desc" },
          { totalViews: "desc" },
          { createdAt: "desc" },
        ],
        take: type === "streams" ? limit : 10,
        skip: type === "streams" ? offset : 0,
      })
    }

    // Search categories
    if (!type || type === "all") {
      results.categories = await prisma.category.findMany({
        where: {
          name: { contains: query, mode: "insensitive" },
        },
        include: {
          _count: {
            select: {
              streams: true,
            },
          },
        },
        orderBy: { name: "asc" },
        take: 5,
      })
    }

    // Search tags
    if (!type || type === "all") {
      results.tags = await prisma.tag.findMany({
        where: {
          name: { contains: query, mode: "insensitive" },
        },
        include: {
          _count: {
            select: {
              streams: true,
            },
          },
        },
        orderBy: { name: "asc" },
        take: 10,
      })
    }

    // Add search analytics (optional)
    const session = await getServerSession(authOptions)
    if (session?.user?.email) {
      const user = await prisma.user.findUnique({
        where: { email: session.user.email },
      })

      if (user) {
        // Log search query for analytics
        await prisma.searchQuery.create({
          data: {
            query,
            type: type || "all",
            userId: user.id,
            resultsCount: {
              users: results.users.length,
              streams: results.streams.length,
              categories: results.categories.length,
              tags: results.tags.length,
            },
          },
        }).catch(() => {
          // Ignore errors in search logging
        })
      }
    }

    return NextResponse.json({
      query,
      type: type || "all",
      results,
      pagination: {
        limit,
        offset,
        hasMore: type === "users" 
          ? results.users.length === limit
          : type === "streams"
          ? results.streams.length === limit
          : false,
      },
    })
  } catch (error) {
    console.error("Search error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Get trending/popular content
export async function POST(req: Request) {
  try {
    const { type, timeframe } = await req.json()

    const results: any = {}

    // Get trending based on timeframe
    const timeframeHours = timeframe === "day" ? 24 : timeframe === "week" ? 168 : 720 // 30 days
    const since = new Date(Date.now() - timeframeHours * 60 * 60 * 1000)

    if (!type || type === "streams") {
      // Trending streams
      results.trendingStreams = await prisma.stream.findMany({
        where: {
          createdAt: { gte: since },
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              image: true,
              followersCount: true,
            },
          },
          category: true,
          tags: true,
        },
        orderBy: [
          { isLive: "desc" },
          { viewerCount: "desc" },
          { totalViews: "desc" },
        ],
        take: 20,
      })
    }

    if (!type || type === "creators") {
      // Trending creators
      results.trendingCreators = await prisma.user.findMany({
        where: {
          role: "creator",
          streams: {
            some: {
              createdAt: { gte: since },
            },
          },
        },
        select: {
          id: true,
          name: true,
          image: true,
          bio: true,
          followersCount: true,
          _count: {
            select: {
              streams: true,
            },
          },
        },
        orderBy: [
          { followersCount: "desc" },
          { totalEarnings: "desc" },
        ],
        take: 20,
      })
    }

    if (!type || type === "categories") {
      // Popular categories
      results.popularCategories = await prisma.category.findMany({
        include: {
          _count: {
            select: {
              streams: {
                where: {
                  createdAt: { gte: since },
                },
              },
            },
          },
        },
        orderBy: {
          streams: {
            _count: "desc",
          },
        },
        take: 10,
      })
    }

    if (!type || type === "tags") {
      // Trending tags
      results.trendingTags = await prisma.tag.findMany({
        include: {
          _count: {
            select: {
              streams: {
                where: {
                  createdAt: { gte: since },
                },
              },
            },
          },
        },
        orderBy: {
          streams: {
            _count: "desc",
          },
        },
        take: 20,
      })
    }

    return NextResponse.json({
      timeframe,
      results,
      generatedAt: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Trending content error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
