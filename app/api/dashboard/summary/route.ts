// app/api/dashboard/summary/route.ts

import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import prisma from "@/lib/prisma"

export async function GET() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.email) {
    return new NextResponse("Unauthorized", { status: 401 })
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
  })

  if (!user) {
    return new NextResponse("User not found", { status: 404 })
  }

  const [totalStreams, totalFollowers, totalViews, totalRevenue, recentStreams, topDonations, trendingFollowers] =
    await Promise.all([
      prisma.stream.count({ where: { creatorId: user.id } }),
      prisma.follow.count({ where: { followingId: user.id } }),
      prisma.stream.aggregate({
        where: { creatorId: user.id },
        _sum: { totalViews: true },
      }),
      prisma.donation.aggregate({
        where: { creatorId: user.id },
        _sum: { amount: true },
      }),
      prisma.stream.findMany({
        where: { creatorId: user.id },
        take: 3,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          title: true,
          isLive: true,
          createdAt: true,
        },
      }),
      prisma.donation.findMany({
        where: { creatorId: user.id },
        take: 3,
        orderBy: { amount: "desc" },
        select: {
          id: true,
          amount: true,
          createdAt: true,
          user: {
            select: { name: true, image: true },
          },
        },
      }),
      prisma.follow.findMany({
        where: { followingId: user.id },
        take: 3,
        orderBy: { createdAt: "desc" },
        select: {
          follower: {
            select: { name: true, image: true },
          },
        },
      }),
    ])

  return NextResponse.json({
    totalStreams,
    totalFollowers,
    totalViews: totalViews._sum.totalViews || 0,
    totalRevenue: totalRevenue._sum.amount || 0,
    recentStreams,
    topDonations,
    trendingFollowers,
  })
}
