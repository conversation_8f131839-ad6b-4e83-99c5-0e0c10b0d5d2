import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { Cashfree } from "@/lib/cashfree"
import { z } from "zod"

const subscriptionSchema = z.object({
  creatorId: z.string(),
  tier: z.enum(["BASIC", "PREMIUM", "VIP"]),
})

const tierPrices = {
  BASIC: 99,
  PREMIUM: 299,
  VIP: 599,
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = subscriptionSchema.parse(body)

    // Get current user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Verify creator exists
    const creator = await prisma.user.findUnique({
      where: { id: validatedData.creatorId },
    })

    if (!creator) {
      return NextResponse.json({ error: "Creator not found" }, { status: 404 })
    }

    // Check if user already has an active subscription to this creator
    const existingSubscription = await prisma.subscription.findUnique({
      where: {
        userId_creatorId: {
          userId: user.id,
          creatorId: validatedData.creatorId,
        },
      },
    })

    if (existingSubscription && existingSubscription.status === "ACTIVE") {
      return NextResponse.json(
        { error: "You already have an active subscription to this creator" },
        { status: 400 }
      )
    }

    const amount = tierPrices[validatedData.tier]
    const currentPeriodEnd = new Date()
    currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1) // 1 month subscription

    // Create subscription record
    const subscription = await prisma.subscription.create({
      data: {
        userId: user.id,
        creatorId: validatedData.creatorId,
        tier: validatedData.tier,
        amount,
        status: "ACTIVE", // Will be updated after payment confirmation
        currentPeriodEnd,
      },
    })

    // Create Cashfree order
    const orderId = `subscription_${subscription.id}_${Date.now()}`
    
    const orderRequest = {
      order_amount: amount,
      order_currency: "INR",
      order_id: orderId,
      customer_details: {
        customer_id: user.id,
        customer_name: user.name || "Anonymous",
        customer_email: user.email!,
        customer_phone: "9999999999", // You should collect this in user profile
      },
      order_meta: {
        return_url: `${process.env.NEXTAUTH_URL}/subscriptions/success?subscriptionId=${subscription.id}`,
        notify_url: `${process.env.NEXTAUTH_URL}/api/payments/webhook`,
      },
    }

    const response = await Cashfree.PGCreateOrder("2023-08-01", orderRequest)
    
    if (response.data) {
      return NextResponse.json({
        subscriptionId: subscription.id,
        paymentSession: response.data.payment_session_id,
        orderId: response.data.order_id,
        amount,
        tier: validatedData.tier,
      })
    } else {
      // Delete the subscription if payment order creation failed
      await prisma.subscription.delete({
        where: { id: subscription.id },
      })
      throw new Error("Failed to create payment order")
    }
  } catch (error) {
    console.error("Subscription creation error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
