import { NextResponse } from "next/server"
import prisma from "@/lib/prisma"

export async function GET() {
  try {
    const metrics = await collectMetrics()
    const prometheusMetrics = formatPrometheusMetrics(metrics)
    
    return new NextResponse(prometheusMetrics, {
      headers: {
        'Content-Type': 'text/plain; version=0.0.4; charset=utf-8',
      },
    })
  } catch (error) {
    console.error("Error collecting metrics:", error)
    return NextResponse.json({ error: "Failed to collect metrics" }, { status: 500 })
  }
}

async function collectMetrics() {
  const [
    totalUsers,
    totalStreams,
    liveStreams,
    totalDonations,
    totalSubscriptions,
    totalRevenue,
    activeUsers,
  ] = await Promise.all([
    prisma.user.count(),
    prisma.stream.count(),
    prisma.stream.count({ where: { isLive: true } }),
    prisma.donation.count({ where: { status: "SUCCESS" } }),
    prisma.subscription.count({ where: { status: "ACTIVE" } }),
    prisma.donation.aggregate({
      where: { status: "SUCCESS" },
      _sum: { amount: true },
    }),
    prisma.user.count({
      where: {
        sessions: {
          some: {
            expires: { gt: new Date() },
          },
        },
      },
    }),
  ])

  // System metrics
  const memoryUsage = process.memoryUsage()
  const cpuUsage = process.cpuUsage()

  return {
    // Business metrics
    totalUsers,
    totalStreams,
    liveStreams,
    totalDonations,
    totalSubscriptions,
    totalRevenue: totalRevenue._sum.amount || 0,
    activeUsers,
    
    // System metrics
    memoryUsage,
    cpuUsage,
    uptime: process.uptime(),
    
    // Node.js metrics
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
  }
}

function formatPrometheusMetrics(metrics: any): string {
  const lines: string[] = []
  
  // Helper function to add metric
  const addMetric = (name: string, value: number, help: string, type: string = 'gauge', labels: Record<string, string> = {}) => {
    lines.push(`# HELP ${name} ${help}`)
    lines.push(`# TYPE ${name} ${type}`)
    
    const labelString = Object.entries(labels)
      .map(([key, value]) => `${key}="${value}"`)
      .join(',')
    
    const labelPart = labelString ? `{${labelString}}` : ''
    lines.push(`${name}${labelPart} ${value}`)
    lines.push('')
  }
  
  // Business metrics
  addMetric('fansxcluziv_users_total', metrics.totalUsers, 'Total number of registered users', 'counter')
  addMetric('fansxcluziv_streams_total', metrics.totalStreams, 'Total number of streams created', 'counter')
  addMetric('fansxcluziv_streams_live', metrics.liveStreams, 'Number of currently live streams')
  addMetric('fansxcluziv_donations_total', metrics.totalDonations, 'Total number of successful donations', 'counter')
  addMetric('fansxcluziv_subscriptions_active', metrics.totalSubscriptions, 'Number of active subscriptions')
  addMetric('fansxcluziv_revenue_total', metrics.totalRevenue, 'Total revenue in currency units', 'counter')
  addMetric('fansxcluziv_users_active', metrics.activeUsers, 'Number of users with active sessions')
  
  // System metrics
  addMetric('nodejs_memory_heap_used_bytes', metrics.memoryUsage.heapUsed, 'Node.js heap memory used in bytes')
  addMetric('nodejs_memory_heap_total_bytes', metrics.memoryUsage.heapTotal, 'Node.js heap memory total in bytes')
  addMetric('nodejs_memory_external_bytes', metrics.memoryUsage.external, 'Node.js external memory in bytes')
  addMetric('nodejs_memory_rss_bytes', metrics.memoryUsage.rss, 'Node.js resident set size in bytes')
  
  addMetric('nodejs_cpu_user_seconds_total', metrics.cpuUsage.user / 1000000, 'Node.js CPU user time in seconds', 'counter')
  addMetric('nodejs_cpu_system_seconds_total', metrics.cpuUsage.system / 1000000, 'Node.js CPU system time in seconds', 'counter')
  
  addMetric('nodejs_uptime_seconds', metrics.uptime, 'Node.js uptime in seconds', 'counter')
  
  // Version info
  addMetric('nodejs_version_info', 1, 'Node.js version info', 'gauge', {
    version: metrics.nodeVersion,
    platform: metrics.platform,
    arch: metrics.arch,
  })
  
  return lines.join('\n')
}
