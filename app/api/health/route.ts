import { NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { Redis } from "ioredis"

const redis = new Redis(process.env.REDIS_URL || "redis://localhost:6379")

export async function GET() {
  const startTime = Date.now()
  const health = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || "unknown",
    environment: process.env.NODE_ENV || "development",
    services: {
      database: { status: "unknown", responseTime: 0 },
      redis: { status: "unknown", responseTime: 0 },
      streaming: { status: "unknown", responseTime: 0 },
    },
    metrics: {
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
    },
  }

  // Check database connection
  try {
    const dbStart = Date.now()
    await prisma.$queryRaw`SELECT 1`
    health.services.database = {
      status: "healthy",
      responseTime: Date.now() - dbStart,
    }
  } catch (error) {
    health.services.database = {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : "Unknown error",
    }
    health.status = "degraded"
  }

  // Check Redis connection
  try {
    const redisStart = Date.now()
    await redis.ping()
    health.services.redis = {
      status: "healthy",
      responseTime: Date.now() - redisStart,
    }
  } catch (error) {
    health.services.redis = {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : "Unknown error",
    }
    health.status = "degraded"
  }

  // Check streaming server (basic check)
  try {
    const streamingStart = Date.now()
    const response = await fetch(`http://${process.env.RTMP_SERVER_URL || "localhost:8000"}/health`, {
      timeout: 5000,
    })
    
    if (response.ok) {
      health.services.streaming = {
        status: "healthy",
        responseTime: Date.now() - streamingStart,
      }
    } else {
      throw new Error(`HTTP ${response.status}`)
    }
  } catch (error) {
    health.services.streaming = {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : "Unknown error",
    }
    health.status = "degraded"
  }

  // Overall response time
  health.responseTime = Date.now() - startTime

  // Determine overall status
  const unhealthyServices = Object.values(health.services).filter(
    service => service.status === "unhealthy"
  ).length

  if (unhealthyServices > 0) {
    health.status = unhealthyServices === Object.keys(health.services).length ? "unhealthy" : "degraded"
  }

  const statusCode = health.status === "healthy" ? 200 : health.status === "degraded" ? 200 : 503

  return NextResponse.json(health, { status: statusCode })
}

// Readiness probe - checks if the application is ready to serve traffic
export async function HEAD() {
  try {
    // Quick database check
    await prisma.$queryRaw`SELECT 1`
    
    // Quick Redis check
    await redis.ping()
    
    return new NextResponse(null, { status: 200 })
  } catch (error) {
    return new NextResponse(null, { status: 503 })
  }
}
