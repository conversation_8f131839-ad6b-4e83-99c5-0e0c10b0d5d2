import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { z } from "zod"
import { watermarkService } from "@/lib/security/watermark"

const profileUpdateSchema = z.object({
  name: z.string().min(2).max(50).optional(),
  bio: z.string().max(500).optional(),
  website: z.string().url().optional(),
  location: z.string().max(100).optional(),
  avatar: z.string().optional(), // Base64 image data
})

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const userId = searchParams.get("userId")

    let targetUserId = userId
    if (!userId) {
      // Get current user's profile
      const currentUser = await prisma.user.findUnique({
        where: { email: session.user.email },
      })
      targetUserId = currentUser?.id
    }

    if (!targetUserId) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const user = await prisma.user.findUnique({
      where: { id: targetUserId },
      select: {
        id: true,
        name: true,
        email: session.user.email === targetUserId ? true : false, // Only show email to self
        image: true,
        bio: true,
        website: true,
        location: true,
        followersCount: true,
        followingCount: true,
        totalEarnings: session.user.email === targetUserId ? true : false, // Only show earnings to self
        availableBalance: session.user.email === targetUserId ? true : false,
        role: true,
        createdAt: true,
        _count: {
          select: {
            streams: true,
            followers: true,
            following: true,
          },
        },
      },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error("Error fetching user profile:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = profileUpdateSchema.parse(body)

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    let updateData: any = {}

    // Handle basic profile updates
    if (validatedData.name) updateData.name = validatedData.name
    if (validatedData.bio) updateData.bio = validatedData.bio
    if (validatedData.website) updateData.website = validatedData.website
    if (validatedData.location) updateData.location = validatedData.location

    // Handle avatar upload
    if (validatedData.avatar) {
      try {
        // Convert base64 to buffer
        const base64Data = validatedData.avatar.replace(/^data:image\/[a-z]+;base64,/, "")
        const imageBuffer = Buffer.from(base64Data, "base64")

        // Add watermark to avatar
        const watermarkedImage = await watermarkService.addImageWatermark(imageBuffer, {
          text: `@${user.name}`,
          position: "bottom-right",
          opacity: 0.2,
          fontSize: 16,
        })

        // In production, you would upload to S3/CDN here
        // For now, we'll store as base64 (not recommended for production)
        const watermarkedBase64 = `data:image/jpeg;base64,${watermarkedImage.toString("base64")}`
        updateData.image = watermarkedBase64

      } catch (error) {
        console.error("Error processing avatar:", error)
        return NextResponse.json(
          { error: "Failed to process avatar image" },
          { status: 400 }
        )
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        bio: true,
        website: true,
        location: true,
        followersCount: true,
        followingCount: true,
        totalEarnings: true,
        availableBalance: true,
        role: true,
        updatedAt: true,
      },
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error("Error updating user profile:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
