import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { z } from "zod"

const followSchema = z.object({
  targetUserId: z.string(),
})

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const { targetUserId } = followSchema.parse(body)

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId },
    })

    if (!targetUser) {
      return NextResponse.json({ error: "Target user not found" }, { status: 404 })
    }

    // Can't follow yourself
    if (user.id === targetUserId) {
      return NextResponse.json({ error: "Cannot follow yourself" }, { status: 400 })
    }

    // Check if already following
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: user.id,
          followingId: targetUserId,
        },
      },
    })

    if (existingFollow) {
      return NextResponse.json({ error: "Already following this user" }, { status: 400 })
    }

    // Create follow relationship
    await prisma.$transaction(async (tx) => {
      // Create follow record
      await tx.follow.create({
        data: {
          followerId: user.id,
          followingId: targetUserId,
        },
      })

      // Update follower count
      await tx.user.update({
        where: { id: targetUserId },
        data: { followersCount: { increment: 1 } },
      })

      // Update following count
      await tx.user.update({
        where: { id: user.id },
        data: { followingCount: { increment: 1 } },
      })
    })

    // Create notification for the followed user
    await prisma.notification.create({
      data: {
        userId: targetUserId,
        type: "FOLLOW",
        title: "New Follower",
        message: `${user.name} started following you`,
        data: {
          followerId: user.id,
          followerName: user.name,
          followerImage: user.image,
        },
      },
    })

    return NextResponse.json({
      message: "Successfully followed user",
      isFollowing: true,
    })
  } catch (error) {
    console.error("Follow error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const targetUserId = searchParams.get("targetUserId")

    if (!targetUserId) {
      return NextResponse.json({ error: "Target user ID required" }, { status: 400 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if following
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: user.id,
          followingId: targetUserId,
        },
      },
    })

    if (!existingFollow) {
      return NextResponse.json({ error: "Not following this user" }, { status: 400 })
    }

    // Remove follow relationship
    await prisma.$transaction(async (tx) => {
      // Delete follow record
      await tx.follow.delete({
        where: {
          followerId_followingId: {
            followerId: user.id,
            followingId: targetUserId,
          },
        },
      })

      // Update follower count
      await tx.user.update({
        where: { id: targetUserId },
        data: { followersCount: { decrement: 1 } },
      })

      // Update following count
      await tx.user.update({
        where: { id: user.id },
        data: { followingCount: { decrement: 1 } },
      })
    })

    return NextResponse.json({
      message: "Successfully unfollowed user",
      isFollowing: false,
    })
  } catch (error) {
    console.error("Unfollow error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const targetUserId = searchParams.get("targetUserId")
    const type = searchParams.get("type") // "followers" or "following"

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    if (targetUserId && type) {
      // Get followers or following list for a specific user
      const follows = await prisma.follow.findMany({
        where: type === "followers" 
          ? { followingId: targetUserId }
          : { followerId: targetUserId },
        include: {
          follower: type === "followers" ? {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true,
              followersCount: true,
            },
          } : false,
          following: type === "following" ? {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true,
              followersCount: true,
            },
          } : false,
        },
        orderBy: { createdAt: "desc" },
        take: 50,
      })

      const users = follows.map(follow => 
        type === "followers" ? follow.follower : follow.following
      )

      return NextResponse.json(users)
    } else if (targetUserId) {
      // Check if current user is following target user
      const isFollowing = await prisma.follow.findUnique({
        where: {
          followerId_followingId: {
            followerId: user.id,
            followingId: targetUserId,
          },
        },
      })

      return NextResponse.json({ isFollowing: !!isFollowing })
    } else {
      // Get current user's following list
      const following = await prisma.follow.findMany({
        where: { followerId: user.id },
        include: {
          following: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true,
              followersCount: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 50,
      })

      const users = following.map(follow => follow.following)
      return NextResponse.json(users)
    }
  } catch (error) {
    console.error("Error fetching follow data:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
