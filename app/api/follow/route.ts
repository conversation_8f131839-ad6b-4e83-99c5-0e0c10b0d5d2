import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import prisma from "@/lib/prisma"

export async function POST(req: Request) {
  const session = await getServerSession(authOptions)

  if (!session || !session.user?.email) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const { targetUserId } = await req.json()

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!currentUser || currentUser.id === targetUserId) {
      return NextResponse.json({ error: "Invalid follow action" }, { status: 400 })
    }

    // Check if already following
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: currentUser.id,
          followingId: targetUserId,
        },
      },
    })

    if (existingFollow) {
      return NextResponse.json({ message: "Already following" }, { status: 200 })
    }

    // Create follow
    await prisma.follow.create({
      data: {
        followerId: currentUser.id,
        followingId: targetUserId,
      },
    })

    // Optional: increment counters
    await prisma.user.update({
      where: { id: currentUser.id },
      data: { followingCount: { increment: 1 } },
    })

    await prisma.user.update({
      where: { id: targetUserId },
      data: { followersCount: { increment: 1 } },
    })

    return NextResponse.json({ message: "Followed successfully" }, { status: 200 })
  } catch (error) {
    console.error("Follow Error:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
}

export async function DELETE(req: Request) {
  const session = await getServerSession(authOptions)

  if (!session || !session.user?.email) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const { targetUserId } = await req.json()

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!currentUser || currentUser.id === targetUserId) {
      return NextResponse.json({ error: "Invalid unfollow action" }, { status: 400 })
    }

    await prisma.follow.delete({
      where: {
        followerId_followingId: {
          followerId: currentUser.id,
          followingId: targetUserId,
        },
      },
    })

    await prisma.user.update({
      where: { id: currentUser.id },
      data: { followingCount: { decrement: 1 } },
    })

    await prisma.user.update({
      where: { id: targetUserId },
      data: { followersCount: { decrement: 1 } },
    })

    return NextResponse.json({ message: "Unfollowed successfully" }, { status: 200 })
  } catch (error) {
    console.error("Unfollow Error:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 })
  }
}
