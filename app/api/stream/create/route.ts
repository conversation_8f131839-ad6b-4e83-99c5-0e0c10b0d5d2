import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import prisma from "@/lib/prisma";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const {
      title,
      description,
      rtmpUrl,
      hlsUrl,
      categoryId,
      tagIds = [], // array of tag IDs
    } = body;

    if (!title) {
      return NextResponse.json({ error: "Title is required" }, { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const stream = await prisma.stream.create({
      data: {
        title,
        description,
        creatorId: user.id,
        rtmpUrl,
        hlsUrl,
        category: categoryId ? { connect: { id: categoryId } } : undefined,
        tags: {
          connect: tagIds.map((id: string) => ({ id })),
        },
      },
      include: {
        creator: true,
        category: true,
        tags: true,
      },
    });

    return NextResponse.json(stream, { status: 201 });
  } catch (error) {
    console.error("[STREAM_CREATE_ERROR]", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
