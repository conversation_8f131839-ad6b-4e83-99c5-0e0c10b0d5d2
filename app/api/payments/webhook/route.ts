import { NextResponse } from "next/server"
import { Cashfree } from "@/lib/cashfree"
import prisma from "@/lib/prisma"
import { logger } from "@/lib/logger"

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const { order_id, payment_status, order_amount, cf_payment_id } = body

    logger.info("Payment webhook received", { order_id, payment_status, order_amount })

    // Verify webhook signature (implement this for production)
    // const signature = req.headers.get('x-webhook-signature')
    // if (!verifyWebhookSignature(body, signature)) {
    //   return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    // }

    // Verify payment with Cashfree
    const paymentDetails = await Cashfree.PGOrderFetchPayments("2023-08-01", order_id)
    
    if (!paymentDetails.data || paymentDetails.data.length === 0) {
      logger.error("Payment not found", { order_id })
      return NextResponse.json({ error: "Payment not found" }, { status: 404 })
    }

    const payment = paymentDetails.data[0]
    
    if (payment.payment_status !== "SUCCESS") {
      logger.warn("Payment not successful", { order_id, status: payment.payment_status })
      return NextResponse.json({ message: "Payment not successful" }, { status: 200 })
    }

    // Determine if this is a donation or subscription based on order_id
    if (order_id.startsWith("donation_")) {
      await handleDonationPayment(order_id, payment)
    } else if (order_id.startsWith("subscription_")) {
      await handleSubscriptionPayment(order_id, payment)
    } else {
      logger.error("Unknown order type", { order_id })
      return NextResponse.json({ error: "Unknown order type" }, { status: 400 })
    }

    return NextResponse.json({ message: "Webhook processed successfully" })
  } catch (error) {
    logger.error("Webhook processing error", { error })
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

async function handleDonationPayment(orderId: string, payment: any) {
  try {
    // Extract donation ID from order_id (format: donation_{id}_{timestamp})
    const donationId = orderId.split("_")[1]
    
    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
      include: { creator: true, user: true },
    })

    if (!donation) {
      logger.error("Donation not found", { donationId })
      return
    }

    // Update donation status
    await prisma.donation.update({
      where: { id: donationId },
      data: {
        status: "SUCCESS",
        updatedAt: new Date(),
      },
    })

    // Update creator earnings
    const creatorShare = donation.amount * 0.9 // 90% to creator, 10% platform fee
    await prisma.user.update({
      where: { id: donation.creatorId },
      data: {
        totalEarnings: { increment: creatorShare },
        availableBalance: { increment: creatorShare },
      },
    })

    logger.info("Donation payment processed", { donationId, amount: donation.amount })
  } catch (error) {
    logger.error("Error processing donation payment", { orderId, error })
  }
}

async function handleSubscriptionPayment(orderId: string, payment: any) {
  try {
    // Extract subscription ID from order_id (format: subscription_{id}_{timestamp})
    const subscriptionId = orderId.split("_")[1]
    
    const subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId },
      include: { creator: true, user: true },
    })

    if (!subscription) {
      logger.error("Subscription not found", { subscriptionId })
      return
    }

    // Update subscription status
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        status: "ACTIVE",
        updatedAt: new Date(),
      },
    })

    // Update creator earnings
    const creatorShare = subscription.amount * 0.9 // 90% to creator, 10% platform fee
    await prisma.user.update({
      where: { id: subscription.creatorId },
      data: {
        totalEarnings: { increment: creatorShare },
        availableBalance: { increment: creatorShare },
      },
    })

    logger.info("Subscription payment processed", { subscriptionId, amount: subscription.amount })
  } catch (error) {
    logger.error("Error processing subscription payment", { orderId, error })
  }
}
