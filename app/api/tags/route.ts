import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { z } from "zod"

const tagSchema = z.object({
  name: z.string().min(1).max(30),
})

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const search = searchParams.get("search")

    let whereClause = {}
    if (search) {
      whereClause = {
        name: {
          contains: search,
          mode: "insensitive",
        },
      }
    }

    const tags = await prisma.tag.findMany({
      where: whereClause,
      include: {
        _count: {
          select: { streams: true },
        },
      },
      orderBy: { name: "asc" },
      take: 50,
    })

    return NextResponse.json(tags)
  } catch (error) {
    console.error("Error fetching tags:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = tagSchema.parse(body)

    // Normalize tag name (lowercase, trim)
    const normalizedName = validatedData.name.toLowerCase().trim()

    // Check if tag already exists
    const existingTag = await prisma.tag.findUnique({
      where: { name: normalizedName },
    })

    if (existingTag) {
      return NextResponse.json(existingTag)
    }

    const tag = await prisma.tag.create({
      data: { name: normalizedName },
    })

    return NextResponse.json(tag, { status: 201 })
  } catch (error) {
    console.error("Error creating tag:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
