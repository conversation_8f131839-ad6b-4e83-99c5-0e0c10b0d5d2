import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import prisma from "@/lib/prisma"
import { z } from "zod"

const messageSchema = z.object({
  content: z.string().min(1).max(500),
  streamId: z.string(),
})

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const streamId = searchParams.get("streamId")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = parseInt(searchParams.get("offset") || "0")

    if (!streamId) {
      return NextResponse.json(
        { error: "Stream ID is required" },
        { status: 400 }
      )
    }

    const messages = await prisma.chatMessage.findMany({
      where: { streamId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
      skip: offset,
    })

    // Reverse to show oldest first
    return NextResponse.json(messages.reverse())
  } catch (error) {
    console.error("Error fetching chat messages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = messageSchema.parse(body)

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Verify stream exists
    const stream = await prisma.stream.findUnique({
      where: { id: validatedData.streamId },
    })

    if (!stream) {
      return NextResponse.json({ error: "Stream not found" }, { status: 404 })
    }

    // Basic content moderation (you can enhance this)
    const bannedWords = ["spam", "scam", "fake"] // Add more as needed
    const containsBannedWords = bannedWords.some(word => 
      validatedData.content.toLowerCase().includes(word)
    )

    if (containsBannedWords) {
      return NextResponse.json(
        { error: "Message contains inappropriate content" },
        { status: 400 }
      )
    }

    const message = await prisma.chatMessage.create({
      data: {
        content: validatedData.content,
        userId: user.id,
        streamId: validatedData.streamId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    })

    return NextResponse.json(message, { status: 201 })
  } catch (error) {
    console.error("Error creating chat message:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
