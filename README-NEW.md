# 🎥 FansXcluziv - Premium Live Streaming Platform

**FansXcluziv.com** is a production-ready live streaming and fan engagement platform built for adult content creators. Featuring real-time streaming, monetization tools, and comprehensive creator analytics.

## 🚀 **Features**

### ✅ **Core Platform**
- **Next.js 14** with App Router and TypeScript
- **PostgreSQL** database with Prisma ORM
- **Redis** for caching and real-time features
- **NextAuth.js** for authentication (Google, GitHub, Credentials)
- **Socket.IO** for real-time chat and notifications
- **Tailwind CSS** with Shadcn/UI components

### ✅ **Live Streaming**
- **RTMP/HLS** streaming infrastructure
- **WebRTC** for low-latency streaming
- **Multi-bitrate** adaptive streaming
- **Stream recording** and VOD
- **Real-time chat** with moderation

### ✅ **Monetization**
- **Cashfree** payment integration
- **Subscription tiers** (Basic, Premium, VIP)
- **Donations/Tips** with real-time alerts
- **Creator payouts** system
- **Revenue analytics**

### ✅ **Security & Content Protection**
- **Content watermarking** (images and videos)
- **AI content moderation**
- **Rate limiting** and DDoS protection
- **Input validation** and sanitization
- **CSRF protection**

### ✅ **Production Infrastructure**
- **Docker** containerization
- **NGINX** reverse proxy and load balancing
- **Prometheus** monitoring
- **Grafana** dashboards
- **CI/CD** with GitHub Actions
- **Health checks** and metrics

---

## 🛠️ **Quick Start**

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 1. Clone Repository
```bash
git clone https://github.com/yourusername/fansxcluziv.git
cd fansxcluziv
```

### 2. Environment Setup
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

### 3. Install Dependencies
```bash
npm install
```

### 4. Database Setup
```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Run migrations
npx prisma migrate deploy
npx prisma generate
```

### 5. Start Development Server
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

---

## 🐳 **Docker Deployment**

### Development
```bash
docker-compose up -d
```

### Production
```bash
# Build and deploy
./scripts/deploy.sh production

# Or manually
docker-compose -f docker-compose.prod.yml up -d
```

---

## 📊 **Monitoring & Analytics**

### Health Checks
- Application: `http://localhost:3000/api/health`
- Metrics: `http://localhost:3000/api/metrics`

### Monitoring Stack
- **Prometheus**: `http://localhost:9090`
- **Grafana**: `http://localhost:3001` (admin/admin)
- **Node Exporter**: `http://localhost:9100`

---

## 🔧 **API Endpoints**

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/signin` - User login
- `GET /api/auth/session` - Get current session

### Streaming
- `POST /api/stream/create` - Create new stream
- `GET /api/streams/live` - Get live streams
- `POST /api/streaming/status` - Update stream status
- `GET /api/streaming/key` - Get stream key

### Payments
- `POST /api/donations/create` - Create donation
- `POST /api/subscriptions/create` - Create subscription
- `POST /api/payments/webhook` - Payment webhook

### Analytics
- `GET /api/analytics/creator` - Creator analytics
- `GET /api/dashboard/summary` - Dashboard summary

---

## 🔒 **Security Features**

### Authentication & Authorization
- JWT-based session management
- Role-based access control
- OAuth integration (Google, GitHub)
- API key authentication

### Content Protection
- Automatic content watermarking
- AI-powered content moderation
- Input validation and sanitization
- XSS and CSRF protection

### Infrastructure Security
- Rate limiting per endpoint
- IP blocking for suspicious activity
- Security headers (HSTS, CSP, etc.)
- Encrypted data transmission

---

## 📈 **Performance & Scalability**

### Caching Strategy
- Redis for session storage
- API response caching
- Static asset optimization
- CDN integration ready

### Database Optimization
- Prisma ORM with connection pooling
- Database indexing
- Query optimization
- Read replicas support

### Monitoring
- Real-time metrics collection
- Performance monitoring
- Error tracking
- Uptime monitoring

---

## 🚀 **Deployment**

### Environment Variables
Copy `.env.example` to `.env.local` and configure:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/fansxcluziv"

# Redis
REDIS_URL="redis://localhost:6379"

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Payment Gateway
CASHFREE_APP_ID="your-cashfree-app-id"
CASHFREE_SECRET_KEY="your-cashfree-secret-key"

# Streaming
RTMP_SERVER_URL="localhost:1935"
HLS_SERVER_URL="localhost:8000"
```

### Production Deployment
1. **Build Docker images**: `docker-compose build`
2. **Run migrations**: `npx prisma migrate deploy`
3. **Start services**: `docker-compose up -d`
4. **Health check**: `curl http://localhost:3000/api/health`

---

## 🧪 **Testing**

### Run Tests
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e
```

### Test Coverage
```bash
npm run test:coverage
```

---

## 📝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🆘 **Support**

- **Documentation**: [docs.fansxcluziv.com](https://docs.fansxcluziv.com)
- **Issues**: [GitHub Issues](https://github.com/yourusername/fansxcluziv/issues)
- **Discord**: [Community Server](https://discord.gg/fansxcluziv)
- **Email**: <EMAIL>

---

**Built with ❤️ for content creators worldwide**
